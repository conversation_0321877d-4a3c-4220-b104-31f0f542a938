import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

import { supabase } from "../db";
import { google } from "googleapis";

const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI;

if (!CLIENT_ID || !CLIENT_SECRET || !REDIRECT_URI) {
  throw new Error("Missing Google OAuth environment variables");
}

const oauth2Client = new google.auth.OAuth2(
  CLIENT_ID,
  CLIENT_SECRET,
  REDIRECT_URI
);

// const refreshGoogleToken = async (userId: string): Promise<string | null> => {
//   try {
//     const { data: credentials, error } = await supabase
//       .from("google_sheets_credentials")
//       .select("access_token, refresh_token")
//       .eq("user_id", userId)
//       .single();

//     if (error || !credentials || !credentials.refresh_token) {
//       console.error("No refresh token found for integration_id:", userId);
//       return null;
//     }
//     oauth2Client.setCredentials({ refresh_token: credentials.refresh_token });
//     const { token: newAccessToken } = await oauth2Client.getAccessToken();
//     if (!newAccessToken) {
//       console.error("Failed to retrieve new access token");
//       return null;
//     }

//     console.log("New access token:", newAccessToken);

//     await supabase
//       .from("google_sheets_credentials")
//       .update({ access_token: newAccessToken })
//       .eq("user_id", userId);

//     return newAccessToken;
//   } catch (err) {
//     console.error("Failed to refresh Google token:", err);
//     return null;
//   }
// };
// const getValidGoogleToken = async (userId: string) => {
//   const { data: credentials, error } = await supabase
//     .from("google_sheets_credentials")
//     .select("access_token, refresh_token")
//     .eq("user_id", userId)
//     .single();

//   if (error || !credentials || !credentials.access_token) {
//     console.error("No valid access token found, attempting to refresh...");
//     return await refreshGoogleToken(userId);
//   }

//   oauth2Client.setCredentials({ access_token: credentials.access_token });
//   const tokenInfo = await oauth2Client
//     .getTokenInfo(credentials.access_token)
//     .catch(() => null);

//   if (!tokenInfo) {
//     console.log("Token expired, refreshing...");
//     return await refreshGoogleToken(userId);
//   }

//   return credentials.access_token;
// };


const refreshGoogleToken = async (credentialId: string): Promise<string | null> => {
  try {
    const { data: record, error } = await supabase
      .from("automate_form_integration_credentials")
      .select("auth_data")
      .eq("id", credentialId)
      .single();

    if (error || !record?.auth_data?.refresh_token) {
      console.error("No refresh token found for credential_id:", credentialId);
      return null;
    }

    const refreshToken = record.auth_data.refresh_token;

    oauth2Client.setCredentials({ refresh_token: refreshToken });

    const { token: newAccessToken } = await oauth2Client.getAccessToken();
    if (!newAccessToken) {
      console.error("Failed to retrieve new access token");
      return null;
    }

    console.log("New access token:", newAccessToken);

    const updatedAuthData = {
      ...record.auth_data,
      access_token: newAccessToken,
    };

    await supabase
      .from("integration_credentials")
      .update({ auth_data: updatedAuthData })
      .eq("id", credentialId);

    return newAccessToken;
  } catch (err) {
    console.error("Failed to refresh Google token:", err);
    return null;
  }
};

const getValidGoogleToken = async (credentialId: string): Promise<string | null> => {
  try {
    const { data: record, error } = await supabase
      .from("automate_form_integration_credentials")
      .select("auth_data")
      .eq("id", credentialId)
      .single();

    const accessToken = record?.auth_data?.access_token;

    if (error || !accessToken) {
      console.warn("No valid access token found, attempting refresh...");
      return await refreshGoogleToken(credentialId);
    }

    oauth2Client.setCredentials({ access_token: accessToken });

    const tokenInfo = await oauth2Client
      .getTokenInfo(accessToken)
      .catch(() => null);

    if (!tokenInfo) {
      console.log("Access token expired or invalid, refreshing...");
      return await refreshGoogleToken(credentialId);
    }

    return accessToken;
  } catch (err) {
    console.error("Error validating token:", err);
    return null;
  }
};


export { getValidGoogleToken };
