import { Hono } from "hono";
import { createTheme,getAllCategories,getThemesGroupedByCategory} from "../controllers/theme.controllers";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const formthemes = new Hono();
formthemes.post("/create", verifySupabaseAuth, createTheme);
formthemes.get("/categories", verifySupabaseAuth, getAllCategories);
formthemes.get("/", verifySupabaseAuth, getThemesGroupedByCategory);

export { formthemes};
