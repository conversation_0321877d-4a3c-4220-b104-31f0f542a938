import { google } from "googleapis";
import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

const GMAIL_SCOPES = ["https://www.googleapis.com/auth/gmail.send"];
const gmailAuth = new google.auth.GoogleAuth({
  credentials: JSON.parse(process.env.GOOGLE_CREDENTIALS || "{}"),
  scopes: GMAIL_SCOPES,
});

const gmail = google.gmail({ version: "v1", auth: gmailAuth });

export const sendEmail = async (to: string, subject: string, body: string) => {
  const message = [
    `To: ${to}`,
    "Subject: " + subject,
    "Content-Type: text/html; charset=utf-8",
    "",
    body,
  ].join("\r\n");

  const encodedMessage = Buffer.from(message).toString("base64");

  await gmail.users.messages.send({
    userId: "me",
    requestBody: { raw: encodedMessage },
  });

  console.log(`✅ Email sent to ${to}`);
};
