import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { UsageService } from "../services/usage.service";

const addWorkspace = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    // Check if user already has a workspace
    const { data: existingWorkspace, error: existingError } = await supabase
      .from("workspaces")
      .select("id")
      .eq("created_by", user.user.id)
      .single();

    if (existingWorkspace) {
      return sendApiError(c, "User can create only one workspace", 400);
    }

    if (existingError && existingError.code !== "PGRST116") {
      console.error("Check Existing Workspace Error:", existingError);
      return sendApiError(c, "Failed to verify existing workspace", 500);
    }

    const body = await c.req.json();
    if (
      !body.name ||
      !body.description ||
      !body.industry ||
      !body.team_size ||
      !body.tags
    ) {
      return sendApiError(c, "All required fields must be provided", 400);
    }
    if (body.team_size < 1) {
      return sendApiError(c, "Team size must be at least 1", 400);
    }

    const newWorkspace = {
      name: body.name,
      description: body.description || null,
      industry: body.industry || null,
      created_by: user.user.id,
      team_size: body.team_size || null,
      status: "Trial",
      trial_expiry: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      // tags: body.tags || null,
    };

    const { data, error } = await supabase
      .from("workspaces")
      .insert([newWorkspace])
      .select("id");

    if (error || !data || data.length === 0) {
      console.error("Create Workspace Error:", error);
      return sendApiError(c, "Failed to create workspace", 500);
    }

    const workspaceId = data[0].id;

    // Update user's workspace ID
    const { error: updateUserError } = await supabase
      .from("user_profile")
      .update({ workspace_id: workspaceId })
      .eq("id", user.user.id);

    if (updateUserError) {
      console.error("Update User Error:", updateUserError);
      return sendApiError(c, "Failed to update user with workspace ID", 500);
    }


    // Add user as workspace member
    const { data: memberData, error: memberInsertError } = await supabase
      .from("workspace_members")
      .insert([
        {
          workspace_id: workspaceId,
          user_id: user.user.id,
          role: "admin",
          status: "Active",
          task_access: true,
          leave_access: true,
          form_access: true,
        },
      ])
      .select("id,workspace_id");

    if (memberInsertError || !memberData || memberData.length === 0) {
      console.error("Add Creator to Members Error:", memberInsertError);
      return sendApiError(c, "Failed to add user to workspace members", 500);
    }

    // Create admin role in automateform_role
    const { data: adminRoleData, error: adminRoleError } = await supabase
      .from("automateform_role")
      .insert([
        {
          name: "admin",
          description: "Administrator with full access to AutomateForm",
          workspace_id: workspaceId,
        },
      ])
      .select("id")
      .single();

    if (adminRoleError || !adminRoleData) {
      console.error("Create Admin Role Error:", adminRoleError);
      return sendApiError(c, "Failed to create admin role", 500);
    }

    // Get all available permissions
    const { data: allPermissions, error: permissionsError } = await supabase
      .from("automateform_permission")
      .select("id");

    if (permissionsError || !allPermissions) {
      console.error("Get Permissions Error:", permissionsError);
      return sendApiError(c, "Failed to get permissions", 500);
    }

    // Assign all permissions to admin role
    const rolePermissions = allPermissions.map((permission) => ({
      role_id: adminRoleData.id,
      permission_id: permission.id,
    }));

    if (rolePermissions.length > 0) {
      const { error: rolePermissionsError } = await supabase
        .from("automateform_role_permission")
        .insert(rolePermissions);

      if (rolePermissionsError) {
        console.error("Assign Role Permissions Error:", rolePermissionsError);
        return sendApiError(
          c,
          "Failed to assign permissions to admin role",
          500
        );
      }
    }

    // Add user to automateform_members with admin role
    const { error: automateformMemberError } = await supabase
      .from("automateform_members")
      .insert([
        {
          workspace_member_id: memberData[0].id,
          workspace_id: workspaceId,
          user_id: user.user.id,
          role_id: adminRoleData.id,
          status: "active",
          joined_at: new Date().toISOString(),
        },
      ]);

    if (automateformMemberError) {
      console.error(
        "Add to Automateform Members Error:",
        automateformMemberError
      );
      return sendApiError(c, "Failed to add user to automateform members", 500);
    }

    return sendApiResponse(c, { workspace_id: workspaceId }, 201);
  } catch (err) {
    console.error("Create Workspace Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getWorkspace = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { data, error } = await supabase
      .from("workspaces")
      .select("*")
      .eq("created_by", user.user.id)
      .single();

    if (error || !data) {
      return sendApiError(c, "Workspace not found", 404);
    }
    return sendApiResponse(c, data, 200);
  } catch (err) {
    console.error("Get Workspace Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const updateWorkspace = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const { id } = c.req.param();
    const body = await c.req.json();

    // Fetch and validate ownership
    const { data: existingWorkspace, error: fetchError } = await supabase
      .from("workspaces")
      .select("created_by")
      .eq("id", id)
      .single();

    if (fetchError || !existingWorkspace) {
      return sendApiError(c, "Workspace not found", 404);
    }

    if (existingWorkspace.created_by !== user.user.id) {
      return sendApiError(c, "Forbidden", 403);
    }

    // Dynamically build update object
    const updatableFields = [
      "name",
      "description",
      "industry",
      "team_size",
      "status",
      "city",
      "state",
      "country",
      "timezone",
      "form_access",
      "workspace_office_location",
      "tags",
      "trial_expiry",
      "t_validity",
      "t_paid_users",
      "la_validity",
      "la_trial_expiry",
      "task_onboarding",
      "referral_code",
      "crm_users",
      "crm_validity",
      "la_users",
      "crm_onboarding",
      "admin_onboarding",
      "admin_boarding_last_remark",
      "assign_onboarding_to",
      "workspace_login_time",
      "workspace_logout_time",
      "form_access",
      "forms_paid_user",
      "forms_total_user",
      "form_onboarding",
      "form_users",
      "form_validity",
    ];

    const updatedFields: Record<string, any> = {};

    for (const key of updatableFields) {
      if (body[key] !== undefined) {
        updatedFields[key] = body[key];
      }
    }

    if (Object.keys(updatedFields).length === 0) {
      return sendApiError(c, "No valid fields provided to update", 400);
    }

    updatedFields.updated_at = new Date().toISOString();

    const { error: updateError } = await supabase
      .from("workspaces")
      .update(updatedFields)
      .eq("id", id);

    if (updateError) {
      console.error("Update Workspace Error:", updateError);
      return sendApiError(c, "Failed to update workspace", 500);
    }

    return sendApiResponse(
      c,
      { message: "Workspace updated successfully" },
      200
    );
  } catch (err) {
    console.error("Update Workspace Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const registerUserToWorkspace = async (c: Context) => {
  try {
    const adminUser = c.get("user");
    if (!adminUser) return sendApiError(c, "Unauthorized", 401);

    const {
      email,
      password,
      role = "Team Member",
      role_id,
      first_name,
      last_name,
      phone,
      reports_to = null,
      task_access = false,
      leave_access = false,
      form_access = false,
      country,
    } = await c.req.json();

    // Validate required fields
    if (!email || !password || !first_name || !last_name || !phone) {
      return sendApiError(
        c,
        "All fields are required: first_name, last_name, phone, email, and password",
        400
      );
    }
    if (form_access) {
      if (!role_id) {
        return sendApiError(c, "role_id is required", 400);
      }
    }
    // Get admin's workspace
    const { data: WorkspaceMember, error: workspaceError } = await supabase
      .from("workspace_members")
      .select("workspace_id")
      .eq("user_id", adminUser.user.id)
      .single();

    if (workspaceError || !WorkspaceMember) {
      return sendApiError(c, "you are not part of any workspace", 400);
    }

    const workspace_id = WorkspaceMember.workspace_id;

    // Check if team member limit has been reached
    const usageData = await UsageService.getWorkspaceUsage(workspace_id);
    if (usageData) {
      // Get current member count
      const { count: currentMemberCount, error: countError } = await supabase
        .from("workspace_members")
        .select("*", { count: "exact" })
        .eq("workspace_id", workspace_id);

      if (!countError && currentMemberCount !== null) {
        // Check if adding this member would exceed the limit
        if (currentMemberCount >= usageData.limits.team_member_limit) {
          return sendApiError(c,
            "Team member limit reached. Please upgrade your plan to add more team members.",
            403
          );
        }
      }
    }

    // Sign up the new user
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp(
      {
        email,
        password,
      }
    );

    if (signUpError || !signUpData?.user?.id) {
      console.error("User signup error:", signUpError);

      return sendApiError(
        c,
        "Failed to register user:" + signUpError?.message,
        500
      );
    }

    const userId = signUpData.user.id;
    const userEmail = signUpData.user.email;
    console.log("User ID:", userId, userEmail);

    // Insert into user_profile
    const { error: profileError } = await supabase.from("user_profile").insert([
      {
        id: userId,
        email,
        first_name,
        last_name,
        phone,
        country,
        workspace_id: workspace_id,
      },
    ]);

    if (profileError) {
      console.error("Insert user_profile error:", profileError);
      return sendApiError(
        c,
        "Failed to create user profile" + profileError.message,
        500
      );
    }

    // Insert into workspace_members
    const {data:workspace_member, error: memberError } = await supabase
      .from("workspace_members")
      .insert([
        {
          workspace_id,
          user_id: userId,
          role,
          status: "Active",
          reports_to,
          task_access,
          leave_access,
          form_access,
        },
      ]).select('id')
      .single();

    if (memberError) {
      console.error("Insert workspace_members error:", memberError);
      return sendApiError(c, "Failed to assign user to workspace", 500);
    }

    // Update team member count in workspace usage
    await UsageService.updateTeamMemberCount(workspace_id);

    if (form_access) {
      const { error: automateformMemberError } = await supabase
        .from("automateform_members")
        .insert([
          {
            workspace_member_id: workspace_member.id,
            workspace_id,
            user_id: userId,
            role_id: role_id,
            status: "active",
            joined_at: new Date().toISOString(),
          },
        ]);

      if (automateformMemberError) {
        console.error(
          "Add to Automateform Members Error:",
          automateformMemberError
        );
        return sendApiError(
          c,
          "Failed to add user to automateform members",
          500
        );
      }
    }

    return sendApiResponse(
      c,
      { message: "User registered and added to workspace" },
      201
    );
  } catch (err) {
    console.error("Register user to workspace error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getWorkspaceMembers = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { workspace_id } = c.req.param();
    const workspaceId = workspace_id;
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Check if user has admin access
    // const isAdmin = await hasAutomateFormAdminAccess(user.user.id, workspaceId);
    // if (!isAdmin) {
    //   return sendApiError(c, "You don't have permission to view workspace members", 403);
    // }

    // Get all workspace members with their user profiles
    const { data: members, error: membersError } = await supabase
      .from("workspace_members")
      .select(
        `
        id,
        user_id,
        role,
        status,
        form_access,
        workspace_id,
        user_profile (
          id,
          first_name,
          last_name,
          email,
          phone,
          profile_image
        )
      `
      )
      .eq("workspace_id", workspaceId)

    if (membersError) {
      console.error("Get Workspace Members Error:", membersError);
      return sendApiError(c, "Failed to fetch workspace members", 500);
    }

    return sendApiResponse(c, { members });
  } catch (err) {
    console.error("Unexpected Error in Fetching Workspace Members:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export {
  addWorkspace,
  getWorkspace,
  updateWorkspace,
  registerUserToWorkspace,
  getWorkspaceMembers,
};
