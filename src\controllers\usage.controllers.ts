import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { UsageService } from "../services/usage.service";

/**
 * Get the current usage statistics for a workspace
 */
// const getWorkspaceUsage = async (c: Context) => {
//   try {
//     const user = c.get("user");
//     if (!user) {
//       return sendApiError(c, "Unauthorized", 401);
//     }

//     const workspaceId = c.req.param("workspace_id");
//     if (!workspaceId) {
//       return sendApiError(c, "Workspace ID is required", 400);
//     }

//     // Verify user belongs to the workspace
//     const { data: userWorkspace, error: userWorkspaceError } = await supabase
//       .from("user_profile")
//       .select("workspace_id")
//       .eq("id", user.user.id)
//       .single();

//     if (userWorkspaceError || !userWorkspace) {
//       return sendApiError(c, "Failed to verify user workspace", 500);
//     }

//     if (userWorkspace.workspace_id !== parseInt(workspaceId)) {
//       return sendApiError(c, "You don't have access to this workspace", 403);
//     }

//     // Get usage statistics
//     const usageData = await UsageService.getWorkspaceUsage(parseInt(workspaceId));
//     if (!usageData) {
//       return sendApiError(c, "Failed to retrieve workspace usage", 500);
//     }

//     // Calculate usage percentages
//     const usagePercentages = {
//       storage: Math.min(100, Math.round((usageData.usage.storage_used_mb / usageData.limits.storage_limit_mb) * 100)),
//       submissions: Math.min(100, Math.round((usageData.usage.submissions_count / usageData.limits.monthly_submissions_limit) * 100)),
//       forms: usageData.limits.create_form_limit ? Math.min(100, Math.round((usageData.usage.create_form_count / usageData.limits.create_form_limit) * 100)) : 0,
//       teamMembers: usageData.limits.team_member_limit ? Math.min(100, Math.round((usageData.usage.team_member_count / usageData.limits.team_member_limit) * 100)) : 0,
//       aiCredits: usageData.limits.ai_credits_limit ? Math.min(100, Math.round((usageData.usage.ai_credits_used / usageData.limits.ai_credits_limit) * 100)) : 0,
//     };

//     // Get available plans for upgrade options
//     const { data: plans } = await supabase
//       .from("module_plans")
//       .select("*")
//       .order("price", { ascending: true });

//     // Check which limits are close to being reached (80% or more)
//     const limitsNearingMax = [];
//     if (usagePercentages.storage >= 80) limitsNearingMax.push('storage');
//     if (usagePercentages.submissions >= 80) limitsNearingMax.push('submissions');
//     if (usagePercentages.forms >= 80) limitsNearingMax.push('forms');
//     if (usagePercentages.teamMembers >= 80) limitsNearingMax.push('teamMembers');
//     if (usagePercentages.aiCredits >= 80) limitsNearingMax.push('aiCredits');

//     // Prepare upgrade recommendations
//     const upgradeRecommendations = limitsNearingMax.length > 0
//       ? {
//           message: `You're approaching the limits of your current plan. Consider upgrading to get more ${limitsNearingMax.join(', ')}.`,
//           limits_nearing_max: limitsNearingMax,
//           available_plans: plans || []
//         }
//       : null;

//     return sendApiResponse(c, {
//       usage: usageData.usage,
//       limits: usageData.limits,
//       percentages: usagePercentages,
//       billing_cycle: {
//         start: usageData.usage.billing_cycle_start,
//         end: usageData.usage.billing_cycle_end,
//         days_remaining: Math.max(0, Math.ceil((new Date(usageData.usage.billing_cycle_end).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))),
//       },
//       upgrade_recommendations: upgradeRecommendations
//     });
//   } catch (err) {
//     console.error("Get Workspace Usage Error:", err);
//     return sendApiError(c, "Internal server error", 500);
//   }
// };

// export { getWorkspaceUsage };

// Add this new function to get detailed workspace usage report
export const getWorkspaceUsageReport = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { workspace_id } = c.req.param();
    if (!workspace_id) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Get current usage data
    const { data: usageData, error: usageError } = await supabase
      .from("automate_form_workspace_usage")
      .select("*")
      .eq("workspace_id", workspace_id)
      .gte("billing_cycle_end", new Date().toISOString())
      .order("billing_cycle_end", { ascending: false })
      .limit(1)
      .single();

    if (usageError) {
      console.error("Error fetching workspace usage:", usageError);
      return sendApiError(c, "Failed to fetch usage data", 500);
    }

    // Get usage limits
    const { data: limitsData, error: limitsError } = await supabase
      .from("automate_form_workspace_usage_limits")
      .select("*")
      .eq("workspace_id", workspace_id)
      .single();

    if (limitsError) {
      console.error("Error fetching workspace limits:", limitsError);
      return sendApiError(c, "Failed to fetch usage limits", 500);
    }

    // Get subscription details
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from("automate_form_module_subscription")
      .select(
        `
        id,
        num_users,
        validity,
        status,
        remarks,
        updated_at,
        app (id, name, caption, icon),
        module (
          id, 
          name, 
          caption, 
          yearly_price, 
          monthly_price,
          features,
          workspace_limit
        )
      `
      )
      .eq("workspace_id", workspace_id)
      .eq("status", "active");

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      return sendApiError(c, "Failed to fetch subscription data", 500);
    }

    // Calculate usage percentages
    const usagePercentages = {
      storage: (usageData.storage_used_mb / limitsData.storage_limit_mb) * 100,
      submissions:
        (usageData.submissions_count / limitsData.monthly_submissions_limit) *
        100,
      forms: usageData.create_form_count
        ? (usageData.create_form_count / limitsData.create_form_limit) * 100
        : 0,
      teamMembers: usageData.team_member_count
        ? (usageData.team_member_count / limitsData.team_member_limit) * 100
        : 0,
      aiCredits: usageData.ai_credits_used
        ? (usageData.ai_credits_used / limitsData.ai_credits_limit) * 100
        : 0,
    };

    // Calculate days remaining in billing cycle
    const daysRemaining = Math.max(
      0,
      Math.ceil(
        (new Date(usageData.billing_cycle_end).getTime() -
          new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )
    );

    // Format subscription data for display
    const formattedSubscriptions = subscriptionData.map((sub) => ({
      id: sub.id,
      app: sub.app,
      plan: sub.module.name,
      features: sub.module.features,
      users: sub.num_users,
      expiresAt: sub.validity,
      daysUntilExpiration: Math.max(
        0,
        Math.ceil(
          (new Date(sub.validity).getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        )
      ),
      status: sub.status,
      workspaceLimits: sub.module.workspace_limit,
    }));

    return sendApiResponse(c, {
      currentUsage: {
        storage: {
          used: usageData.storage_used_mb,
          limit: limitsData.storage_limit_mb,
          percentage: usagePercentages.storage.toFixed(1),
        },
        submissions: {
          used: usageData.submissions_count,
          limit: limitsData.monthly_submissions_limit,
          percentage: usagePercentages.submissions.toFixed(1),
        },
        forms: {
          used: usageData.create_form_count || 0,
          limit: limitsData.create_form_limit,
          percentage: usagePercentages.forms.toFixed(1),
        },
        teamMembers: {
          used: usageData.team_member_count || 0,
          limit: limitsData.team_member_limit,
          percentage: usagePercentages.teamMembers.toFixed(1),
        },
        aiCredits: {
          used: usageData.ai_credits_used || 0,
          limit: limitsData.ai_credits_limit,
          percentage: usagePercentages.aiCredits.toFixed(1),
        },
      },
      billingCycle: {
        start: usageData.billing_cycle_start,
        end: usageData.billing_cycle_end,
        daysRemaining,
      },
      planType: limitsData.plan_type,
      subscriptions: formattedSubscriptions,
    });
  } catch (err) {
    console.error("Get Workspace Usage Report Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
