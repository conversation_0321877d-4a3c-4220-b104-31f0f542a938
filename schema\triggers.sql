-- Trigger function to update wallet balance
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
  -- Update wallet balance based on transaction amount
  UPDATE workspace_wallet
  SET 
    balance = balance + NEW.amount,
    updated_at = NOW()
  WHERE workspace_id = NEW.workspace_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to initialize workspace with free plan on creation
CREATE OR REPLACE FUNCTION initialize_workspace_free_plan()
RETURNS TRIGGER AS $$
DECLARE
    user_country TEXT;
BEGIN
    -- Get the user's country from user_profile
    SELECT country INTO user_country
    FROM user_profile
    WHERE id = NEW.created_by;
    
    -- Create wallet for new workspace with currency based on country
    INSERT INTO workspace_wallet (
      workspace_id,
      balance,
      currency
    ) VALUES (
      NEW.id,
      0,
      CASE 
        WHEN user_country = 'India' THEN 'INR'
        ELSE 'USD'
      END
    );
  
  -- Create usage limits with default values
  INSERT INTO workspace_usage_limits (
    workspace_id,
    storage_limit_mb,
    monthly_form_responses_limit,
    plan_type,
    create_form_limit,
    team_member_limit,
    ai_credits_limit,
    monthly_submissions_limit
  ) VALUES (
    NEW.id,
    100, -- Default 1GB
    100, -- Default 1000 responses
    'free', -- Default free plan
    10, -- Default 10 forms
    10, -- Default 10 team members
    10, -- Default 100 AI credits
    100-- Default free plan
  );
  
  -- Initialize usage tracking
  INSERT INTO workspace_usage (
    workspace_id,
    storage_used_mb,
    form_responses_count,
    form_created_count,
    team_member_count,
    ai_credits_used,
    submissions_count,
    billing_cycle_start,
    billing_cycle_end
  ) VALUES (
    NEW.id,
    0,
    0,
    0,
    0,
    0,
    0,
    DATE_TRUNC('month', NOW()),
    (DATE_TRUNC('month', NOW()) + INTERVAL '1 month' - INTERVAL '1 day')::DATE
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to reset monthly usage counters
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS TRIGGER AS $$
DECLARE
  current_month DATE := DATE_TRUNC('month', NOW())::DATE;
  next_month DATE := (DATE_TRUNC('month', NOW()) + INTERVAL '1 month')::DATE;
BEGIN
  -- If we're in a new billing cycle, create a new usage record
  IF current_month > OLD.billing_cycle_end THEN
    -- Update the existing record to mark it as completed
    UPDATE workspace_usage
    SET updated_at = NOW()
    WHERE id = OLD.id;
    
    -- Create a new usage record for the new billing cycle
    INSERT INTO workspace_usage (
      workspace_id,
      storage_used_mb,
      form_responses_count,
      form_created_count,
      team_member_count,
      ai_credits_used,
      submissions_count,
      billing_cycle_start,
      billing_cycle_end
    ) VALUES (
      OLD.workspace_id,
      OLD.storage_used_mb, -- Keep storage usage
      0, -- Reset form responses count
      0, -- Reset form created count
      current_month,
      (current_month + INTERVAL '1 month' - INTERVAL '1 day')::DATE
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER workspace_creation_trigger
AFTER INSERT ON workspaces
FOR EACH ROW
EXECUTE FUNCTION initialize_workspace_free_plan();

CREATE TRIGGER monthly_usage_reset_trigger
BEFORE UPDATE ON workspace_usage
FOR EACH ROW
EXECUTE FUNCTION reset_monthly_usage();
