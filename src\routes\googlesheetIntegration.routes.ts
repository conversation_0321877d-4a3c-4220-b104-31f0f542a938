

import { Hono } from "hono";
import {createGoogleSheet, linkFormToGoogleSheet,getUserSheets,getActionforGoogleSheet,getUserIntegrationCredentials, getsheetData} from "../controllers/googlesheetintegration.controllers";
import { addUserConnection } from "../controllers/googleAuthController";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const googlesheet = new Hono();

googlesheet.get("/action/:id",verifySupabaseAuth,getActionforGoogleSheet );
googlesheet.get("/connection/:id",verifySupabaseAuth,getUserIntegrationCredentials );
googlesheet.get('/addconnection',verifySupabaseAuth,addUserConnection);
googlesheet.post("/",verifySupabaseAuth,getUserSheets );
googlesheet.post("/sheetdetails",verifySupabaseAuth,getsheetData );
googlesheet.post("/link",verifySupabaseAuth, linkFormToGoogleSheet);
googlesheet.post("/create",verifySupabaseAuth, createGoogleSheet);

export {googlesheet};
