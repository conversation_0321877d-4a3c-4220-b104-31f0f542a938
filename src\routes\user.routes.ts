import { Hono } from "hono";
import {loginUser,registerUser,logoutUser,uploadProfilePic,refreshSession,changePassword,updateUserProfile,googleCallback,loginWithGoogle,forgotPassword, resetPassword,getUser,getUserRole, createProfile, checkAuth, removeProfileImage} from "../controllers/user.controller"
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const user = new Hono();
user.post("/login",loginUser);
user.get("/google-login",loginWithGoogle);
user.get("/google-callback",googleCallback);
user.post("/register",registerUser);
user.post("/logout",verifySupabaseAuth,logoutUser);
user.post("/uploadimage",verifySupabaseAuth,uploadProfilePic);
user.get("/refreshSession",refreshSession);
user.post("/changepassword",verifySupabaseAuth,changePassword);
user.post("/updateprofile",verifySupabaseAuth,updateUserProfile);
user.post("/forgot_password",forgotPassword)
user.post("/reset_password",resetPassword)
user.get("/profile",verifySupabaseAuth,getUser)
user.get("/role",verifySupabaseAuth,getUserRole)
user.post("/createprofile",verifySupabaseAuth,createProfile)
user.get("/check-auth", verifySupabaseAuth, checkAuth);
user.delete("/remove-profile-image", verifySupabaseAuth, removeProfileImage);
// user.post("/updaterole",verifySupabaseAuth,updateUserRole)
export { user};
