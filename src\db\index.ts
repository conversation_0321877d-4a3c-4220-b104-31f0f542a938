import { createClient } from '@supabase/supabase-js';
import dotenv from "dotenv";

dotenv.config({ path: "./.env" });

const supabaseUrl = process.env.SUPABASE_URL;
const key = process.env.SUPABASE_KEY;

if (!supabaseUrl || !key) {
  console.error('Missing SUPABASE_URL or SERVICE_ROLE_KEY in environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, key);

export { supabase };
