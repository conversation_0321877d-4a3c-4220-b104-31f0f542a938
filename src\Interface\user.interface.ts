interface LoginRequest {
  email: string;
  password: string;
}

interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  country:string;
  countryCode:string;
  terms_conditions:boolean;
}
interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

export type { LoginRequest, RegisterRequest,ChangePasswordRequest };