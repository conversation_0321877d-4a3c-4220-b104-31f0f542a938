import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { getWalletBalance, addFundsToWallet, getWalletTransactions, createOrder, getModulePlans } from "../controllers/billing.controllers";

const billingRoutes = new Hono();

// Existing routes
billingRoutes.get("/balance/:workspace_id", verifySupabaseAuth, getWalletBalance);
billingRoutes.post("/add-funds/:workspace_id", verifySupabaseAuth, addFundsToWallet);
billingRoutes.get("/transactions/:workspace_id", verifySupabaseAuth, getWalletTransactions);
billingRoutes.post("/create-order/:workspace_id", verifySupabaseAuth, createOrder);

// New route for getting module plans
billingRoutes.get("/plans/:module_id", getModulePlans);

export { billingRoutes };
