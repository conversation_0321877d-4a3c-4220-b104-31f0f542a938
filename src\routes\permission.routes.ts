import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { 
  getAllPermissions, 
  createPermission, 
  updatePermission, 
  deletePermission 
} from "../controllers/permission.controllers";

const permission = new Hono();

// Apply auth middleware to all routes
permission.use("*", verifySupabaseAuth);

// Permission routes
permission.get("/:id", getAllPermissions);
permission.post("/", createPermission);
permission.put("/:id", updatePermission);
permission.delete("/:id", deletePermission);

export { permission };
