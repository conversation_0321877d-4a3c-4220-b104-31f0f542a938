import dotenv from "dotenv";
dotenv.config({ path: "./.env" });
import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

const getIntegration = async (c: Context) => {
  try {
    const user = c.get('user');
    const formId = c.req.param('formId');

    if (!user) return sendApiError(c, "Unauthorized", 401);
    if (!formId) return sendApiError(c, "Form ID is required", 400);

    // First, fetch all available integrations
    const { data: integrations, error: integrationsError } = await supabase
      .from('automate_form_integrations')
      .select('id, name, status, description, icon');

    if (integrationsError) return sendApiError(c, "Failed to fetch integrations", 500);

    // Then, fetch all connected integrations for this form
    const { data: formIntegrations, error: formIntegrationsError } = await supabase
      .from('form_integrations')
      .select(`
        id,
        integration_id,
        credential_id,
        action_id,
        enabled,
        metadata,
        mapped_data,
        automate_form_integration_credentials (
          id,
          name,
          auth_type,
          created_at
        )
      `)
      .eq('form_id', formId)
      .eq('created_by', user.user.id);
console.log("formIntegrationsError",formIntegrationsError)
    if (formIntegrationsError) {
      console.error("Error fetching form integrations:", formIntegrationsError);
      return sendApiError(c, "Failed to fetch form integrations", 500);
    }
console.log("connectedIntegrations",integrations);
    // Map the integrations with their connection status and credentials
    const mappedIntegrations = integrations.map(integration => {
      const connectedIntegrations = formIntegrations?.filter(
        fi => fi.integration_id === integration.id
      ) || [];

      return {
        ...integration,
        isConnected: connectedIntegrations.length > 0,
        connections: connectedIntegrations.map(ci => ({
          formIntegatedId: ci.id,
          credentialId: ci.credential_id,
          credentialName: ci.automate_form_integration_credentials[0]?.name,
          actionId: ci.action_id,
          enabled: ci.enabled,
          connectedAt: ci.automate_form_integration_credentials[0]?.created_at,
          metadata: ci.metadata,
          mappedData: ci.mapped_data
        }))
      };
    });

    return sendApiResponse(c, {
      message: "Integrations retrieved successfully",
      data: mappedIntegrations
    });

  } catch (err) {
    console.error("API Error:", err);
    return sendApiError(c, "Internal Server Error", 500);
  }
};
export { getIntegration };



