# 🚀 Bun Backend

This is a backend project built using [Bun](https://bun.sh), a fast all-in-one JavaScript runtime.  

## 📦 Installation

Ensure you have [Bun](https://bun.sh/docs/installation) installed on your system.  
To install dependencies, run:

```sh
bun install
```




## ▶️ Running the Project Locally

Start the development server using:

```sh
bun start
```

Or manually run the main file:

```sh
bun start
```

## 🐳 Running with Docker

### 1️⃣ Build the Docker Image
```sh
docker build -t bun-backend .
```

### 2️⃣ Run the Container
```sh
docker run -p 3000:3000 --env-file .env bun-backend
```

This will start your backend inside a Docker container, using environment variables from `.env`.

## 📁 Project Structure

```
/bun-backend
├── src/
│   ├── controllers/     # Controllers handling business logic
│   ├── db/             # Database configurations or queries
│   ├── Interface/      # TypeScript interfaces
│   ├── middleware/     # Middleware functions
│   ├── models/         # Database models
│   ├── routes/         # API routes
│   ├── utils/          # Utility functions
│   ├── app.ts          # Main application setup
│   ├── constant.ts     # Constants and configuration values
│   ├── index.ts        # Entry point of the application
├── .dockerignore       # Docker ignore file
├── .env                # Environment variables
├── .gitignore          # Git ignore file
├── .prettierignore     # Prettier ignore rules
├── .prettierrc         # Prettier configuration
├── bun.lock            # Bun dependency lock file
├── Dockerfile          # Docker configuration
├── package.json        # Project metadata
├── README.md           # Project documentation
└── tsconfig.json       # TypeScript configuration
```

## 🔗 About Bun

This project was created using:
```sh
bun init
```
in **Bun v1.2.2**.

Learn more about Bun at [bun.sh](https://bun.sh).

