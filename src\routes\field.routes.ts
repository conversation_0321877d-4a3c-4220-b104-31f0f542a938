import { Hono } from "hono";
import {getForm<PERSON>ields, createForm<PERSON>ield, updateForm<PERSON>ield, updatecondition<PERSON><PERSON><PERSON>ield} from "../controllers/field.controllers"
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const field = new Hono();
field.post("/createfields",verifySupabaseAuth,createForm<PERSON>ield);
field.put("/updatefields/:id",verifySupabaseAuth,updateFormField);
field.put("/updatecondition/:id",verifySupabaseAuth,updateconditionFormField);
field.get("/:form_id",verifySupabaseAuth,getFormFields);
field.delete("/",verifySupabaseAuth,getFormFields);
export {field};
