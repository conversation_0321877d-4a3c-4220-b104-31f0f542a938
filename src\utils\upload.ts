import { supabase } from "../db/index";
import { writeFile, unlink } from "fs/promises";
import path from "path";
import { UsageService } from "../services/usage.service";

/**
 * Upload a file to Supabase storage
 * @param file The file to upload
 * @param bucket_name The bucket name to upload to
 * @param workspaceId Optional workspace ID to organize files by workspace
 * @param trackUsage Whether to track storage usage (default: true)
 * @returns The public URL of the uploaded file
 */
export const upload = async (
  file: File,
  bucket_name: string,
  workspaceId?: number,
  trackUsage: boolean = true
): Promise<string> => {
  if (!file) throw new Error("No file provided");
  console.log("file", file);
  console.log("bucket_name", bucket_name);

  // Calculate file size in MB for usage tracking
  const fileSizeMB = file.size / (1024 * 1024);

  // Create folder path based on workspace if provided
  const folderPath = workspaceId ? `workspace_${workspaceId}/` : '';
  const tempFileName = `${Date.now()}-${file.name}`;
  const tempPath = path.join(process.cwd(), "public/temp", tempFileName);
  const buffer = Buffer.from(await file.arrayBuffer());
  await writeFile(tempPath, buffer);

  try {
    // Upload to workspace-specific folder if workspaceId is provided
    const filePath = `${folderPath}${tempFileName}`;

    const { data, error } = await supabase.storage
      .from(bucket_name)
      .upload(filePath, buffer, {
        contentType: file.type,
      });

    console.log("data", data);
    if (error || !data)
      throw new Error(`Upload failed: ${error?.message || "Unknown error"}`);

    const publicUrl = supabase.storage.from(bucket_name).getPublicUrl(data.path).data.publicUrl;

    console.log("Upload successful! Public URL:", publicUrl);

    // Track storage usage if workspaceId is provided and tracking is enabled
    if (workspaceId && trackUsage) {
      await UsageService.updateStorageUsage(workspaceId, fileSizeMB);
    }

    return publicUrl;
  } catch (err) {
    console.error("Upload error:", err);
    throw new Error(`Upload failed: ${(err as Error).message}`);
  } finally {
    await unlink(tempPath).catch((err) =>
      console.error("Failed to delete temp file:", err)
    );
  }
};
