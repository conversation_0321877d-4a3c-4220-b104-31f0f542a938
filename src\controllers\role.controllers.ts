import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import {
  hasAutomateFormAdminAccess,
  hasAutomateFormPermission,
} from "../utils/RoleUtils";

const getWorkspaceRoles = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const workspaceId = c.req.param("workspace_id");
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }
    console.log("📢 Workspace ID:", workspaceId);
    const { data: rolesWithCount, error: rolesError } = await supabase.rpc(
      "get_workspace_roles_with_member_count",
      { workspace_id_param: workspaceId }
    );

    if (rolesError) {
      console.error("Get Roles Error:", rolesError);
      return sendApiError(c, "Failed to fetch roles", 500);
    }
    return sendApiResponse(c, { roles: rolesWithCount });
  } catch (err) {
    console.error("Unexpected Error in Fetching Roles:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Create a new role
const createRole = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { name, description, workspaceId } = body;

    if (!name || !workspaceId) {
      return sendApiError(c, "Role name and workspace ID are required", 400);
    }

    // Check if user is an admin
    // const isAdmin = await hasAutomateFormAdminAccess(user.user.id, workspaceId);
    // if (!isAdmin) {
    //   return sendApiError(c, "Only administrators can create roles", 403);
    // }

    // Start a transaction
    // First create the role
    const { data: role, error: roleError } = await supabase
      .from("automateform_role")
      .insert([{ name, description, workspace_id: workspaceId }])
      .select()
      .single();

    if (roleError) {
      if (roleError.code === "23505") {
        // Unique violation
        return sendApiError(
          c,
          "Role with this name already exists in this workspace",
          409
        );
      }
      console.error("Create Role Error:", roleError);
      return sendApiError(c, "Failed to create role", 500);
    }

    return sendApiResponse(
      c,
      {
        message: "Role created successfully",
        role,
      },
      201
    );
  } catch (err) {
    console.error("Create Role Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Update a role
const updateRole = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const roleId = c.req.param("id");
    if (!roleId) {
      return sendApiError(c, "Role ID is required", 400);
    }

    const body = await c.req.json();
    const { name, description, permissionIds } = body;

    // Get the role to check workspace and validate user access
    const { data: existingRole, error: roleError } = await supabase
      .from("automateform_role")
      .select("workspace_id")
      .eq("id", roleId)
      .single();

    if (roleError || !existingRole) {
      return sendApiError(c, "Role not found", 404);
    }

    // Update role data if name or description provided
    const updateData: any = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;

    if (Object.keys(updateData).length > 0) {
      const { error: updateError } = await supabase
        .from("automateform_role")
        .update(updateData)
        .eq("id", roleId);

      if (updateError) {
        if (updateError.code === "23505") {
          // Unique violation
          return sendApiError(
            c,
            "Role with this name already exists in this workspace",
            409
          );
        }
        console.error("Update Role Error:", updateError);
        return sendApiError(c, "Failed to update role", 500);
      }
    }

    // If permissions are provided, use our PostgreSQL function to update role permissions
    if (permissionIds && Array.isArray(permissionIds)) {
      const { error: permissionError } = await supabase.rpc(
        'upsert_role_permissions',
        { 
          p_role_id: roleId,
          p_permission_ids: permissionIds
        }
      );
      
      if (permissionError) {
        console.error("Update Permissions Error:", permissionError);
        return sendApiError(c, "Failed to update role permissions", 500);
      }
    }

    // Get updated role with permissions
    const { data: updatedRole, error: getError } = await supabase
      .from("automateform_role")
      .select(
        `
        id,
        name,
        description,
        workspace_id,
        automateform_role_permission (
          id,
          permission_id,
          automateform_permission (
            id,
            code
          )
        )
      `
      )
      .eq("id", roleId)
      .single();

    if (getError) {
      console.error("Get Updated Role Error:", getError);
      return sendApiError(
        c,
        "Role updated but failed to retrieve updated data",
        500
      );
    }

    return sendApiResponse(c, {
      message: "Role updated successfully",
      role: updatedRole,
    });
  } catch (err) {
    console.error("Update Role Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Delete a role
const deleteRole = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const roleId = c.req.param("id");
    if (!roleId) {
      return sendApiError(c, "Role ID is required", 400);
    }

    // Get the role to check workspace and validate user access
    const { data: existingRole, error: roleError } = await supabase
      .from("automateform_role")
      .select("workspace_id")
      .eq("id", roleId)
      .single();

    if (roleError || !existingRole) {
      return sendApiError(c, "Role not found", 404);
    }

    // Check if user is an admin
    const isAdmin = await hasAutomateFormAdminAccess(
      user.user.id,
      existingRole.workspace_id
    );
    if (!isAdmin) {
      return sendApiError(c, "Only administrators can delete roles", 403);
    }

    // Check if role is assigned to any members
    const { data: usedRoles, error: checkError } = await supabase
      .from("automateform_members")
      .select("id")
      .eq("role_id", roleId)
      .limit(1);

    if (checkError) {
      console.error("Check Role Usage Error:", checkError);
      return sendApiError(c, "Failed to check if role is in use", 500);
    }

    if (usedRoles && usedRoles.length > 0) {
      return sendApiError(
        c,
        "Cannot delete role as it is assigned to one or more users",
        409
      );
    }

    // Delete role (this will cascade delete role permissions due to foreign key constraint)
    const { error: deleteError } = await supabase
      .from("automateform_role")
      .delete()
      .eq("id", roleId);

    if (deleteError) {
      console.error("Delete Role Error:", deleteError);
      return sendApiError(c, "Failed to delete role", 500);
    }

    return sendApiResponse(c, {
      message: "Role deleted successfully",
    });
  } catch (err) {
    console.error("Delete Role Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};



export {
  getWorkspaceRoles,
  createRole,
  updateRole,
  deleteRole,
};


