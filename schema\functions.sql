-- Function to safely increment wallet balance
CREATE OR <PERSON><PERSON>LACE FUNCTION increment_wallet_balance(wallet_id UUID, increment_amount DECIMAL)
RETURNS VOID AS $$
BEGIN
  UPDATE workspace_wallet
  SET 
    balance = balance + increment_amount,
    updated_at = NOW()
  WHERE id = wallet_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check and update usage when a form response is submitted
CREATE OR REPLACE FUNCTION update_form_response_usage()
RETURNS TRIGGER AS $$
DECLARE
  form_workspace_id BIGINT;
  current_usage RECORD;
  usage_limits RECORD;
  wallet_balance DECIMAL;
  response_cost DECIMAL := 0.01; -- Cost per form response
BEGIN
  -- Get the workspace_id from the form using the form_id in the response
  SELECT workspace_id INTO form_workspace_id
  FROM forms
  WHERE id = NEW.form_id;
  
  -- If no workspace_id found, log and exit
  IF form_workspace_id IS NULL THEN
    RAISE NOTICE 'No workspace_id found for form_id: %', NEW.form_id;
    RETURN NEW;
  END IF;
  
  -- Get current usage for this workspace
  SELECT * INTO current_usage
  FROM workspace_usage
  WHERE workspace_id = form_workspace_id
  AND NOW() BETWEEN billing_cycle_start AND billing_cycle_end;
  
  -- Get usage limits for this workspace
  SELECT * INTO usage_limits
  FROM workspace_usage_limits
  WHERE workspace_id = form_workspace_id;
  
  -- If no usage limits found, create default limits
  IF usage_limits IS NULL THEN
    INSERT INTO workspace_usage_limits (
      workspace_id,
      storage_limit_mb,
      monthly_form_responses_limit,
      plan_type
    ) VALUES (
      form_workspace_id,
      1000, -- Default 1GB
      1000, -- Default 1000 responses
      'basic' -- Default plan
    )
    RETURNING * INTO usage_limits;
  END IF;
  
  -- If no current usage record exists, create one
  IF current_usage IS NULL THEN
    INSERT INTO workspace_usage (
      workspace_id,
      form_responses_count,
      billing_cycle_start,
      billing_cycle_end
    ) VALUES (
      form_workspace_id,
      1,
      DATE_TRUNC('month', NOW()),
      (DATE_TRUNC('month', NOW()) + INTERVAL '1 month' - INTERVAL '1 day')::DATE
    );
  ELSE
    -- Update the form responses count
    UPDATE workspace_usage
    SET 
      form_responses_count = form_responses_count + 1,
      updated_at = NOW()
    WHERE id = current_usage.id;
    
    -- Check if we've exceeded the limit
    IF current_usage.form_responses_count >= usage_limits.monthly_form_responses_limit THEN
      -- Get wallet balance
      SELECT balance INTO wallet_balance
      FROM workspace_wallet
      WHERE workspace_id = form_workspace_id;
      
      -- If no wallet exists, create one with zero balance
      IF wallet_balance IS NULL THEN
        INSERT INTO workspace_wallet (
          workspace_id,
          balance
        ) VALUES (
          form_workspace_id,
          0
        )
        RETURNING balance INTO wallet_balance;
      END IF;
      
      -- Record the transaction
      INSERT INTO wallet_transactions (
        workspace_wallet_id,
        amount,
        transaction_type,
        description,
        status
      ) VALUES (
        (SELECT id FROM workspace_wallet WHERE workspace_id = form_workspace_id),
        response_cost,
        'charge',
        'Form response charge (over limit)',
        CASE WHEN wallet_balance >= response_cost THEN 'completed' ELSE 'failed' END
      );
      
      -- If balance is sufficient, deduct the amount
      IF wallet_balance >= response_cost THEN
        UPDATE workspace_wallet
        SET 
          balance = balance - response_cost,
          updated_at = NOW()
        WHERE workspace_id = form_workspace_id;
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update usage when a form response is submitted
CREATE TRIGGER update_form_response_usage_trigger
AFTER INSERT ON form_responses
FOR EACH ROW
EXECUTE FUNCTION update_form_response_usage();





