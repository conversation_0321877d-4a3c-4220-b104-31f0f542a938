import dotenv from "dotenv";
dotenv.config({ path: "./.env" });
import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { upload } from "../utils/upload";


const createTheme = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) return sendApiError(c, "Unauthorized", 401);
      const formData = await c.req.formData();
      const name = formData.get("name")?.toString();
      const type = formData.get("type")?.toString() || "image";
      const category_id = formData.get("category_id")?.toString();
  
      if (!name || !category_id || !type || !["image"].includes(type) ) {
        return sendApiError(c, "Name and Category ID are required", 400);
      }
  
      // Validate category exists
      const { data: category, error: catError } = await supabase
        .from("automate_form_theme_categories")
        .select("id")
        .eq("id", category_id)
        .single();
  
      if (catError || !category) {
        return sendApiError(c, "Invalid category", 404);
      }
  
      // Upload images if provided
      let imageUrl = "";
      let iconImageUrl = "";
  
      const imageFile = formData.get("image") as File | null;
      const iconImageFile = formData.get("iconImage") as File | null;
      if (!imageFile || !iconImageFile ) {
        return sendApiError(c, "imageFile and iconImageFile are missing", 400);
      }
      if (imageFile && imageFile.name) {
        imageUrl = await upload(imageFile, "assets");
      }
  
      if (iconImageFile && iconImageFile.name) {
        iconImageUrl = await upload(iconImageFile, "assets");
      }
  
      const newTheme = {
        name,
        type,
        image: imageUrl,
        iconimage: iconImageUrl,
        category_id,
      };
  
      const { data, error } = await supabase
        .from("automate_form_themes")
        .insert([newTheme])
        .select("id, name");
  
      if (error) {
        console.error("Create Theme Error:", error);
        return sendApiError(c, "Failed to create theme", 500);
      }
  
      return sendApiResponse(c, { theme: data[0] }, 201);
    } catch (err) {
      console.error("Create Theme Unexpected Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };
const getAllCategories = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) return sendApiError(c, "Unauthorized", 401);
  
      const { data: categories, error } = await supabase
        .from("automate_form_theme_categories")
        .select("id, category");
  
      if (error) {
        console.error("Get All Categories Error:", error);
        return sendApiError(c, "Failed to retrieve categories", 500);
      }
  
      if (!categories || categories.length === 0) {
        return sendApiError(c, "No categories found", 404);
      }
  
      return sendApiResponse(c, { categories }, 200);
    } catch (err) {
      console.error("Get All Categories Unexpected Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };

  const getThemesGroupedByCategory = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) return sendApiError(c, "Unauthorized", 401);
  
      // 1. Get all categories
      const { data: categories, error: categoryError } = await supabase
        .from("automate_form_theme_categories")
        .select("id, category");
  
      if (categoryError || !categories) {
        console.error("Category Fetch Error:", categoryError);
        return sendApiError(c, "Failed to fetch categories", 500);
      }
  
      const themesByCategory = [];
  
      // 2. For each category, fetch its themes
      for (const cat of categories) {
        const { data: themes, error: themeError } = await supabase
          .from("automate_form_themes")
          .select("id, name, image, iconimage, type")
          .eq("category_id", cat.id);
  
        if (themeError) {
          console.error(`Theme Fetch Error for category ${cat.category}:`, themeError);
          return sendApiError(c, "Failed to fetch themes", 500);
        }
  
        themesByCategory.push({
          id: cat.id,
          category: cat.category,
          themes,
        });
      }
  
      return sendApiResponse(c, { themes_by_category: themesByCategory }, 200);
    } catch (err) {
      console.error("Grouped Themes Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };
  
  export {createTheme,getAllCategories,getThemesGroupedByCategory}