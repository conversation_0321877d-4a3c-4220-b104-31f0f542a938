import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import {
  createTicket,
  getUserTickets,
  getTicketById,
  updateTicket,
  deleteTicket,
  sendChatMessage,
  getTicketMessages
} from "../controllers/support.controllers";

const supportRoutes = new Hono();
supportRoutes.use("*", verifySupabaseAuth);

supportRoutes.post("/tickets", createTicket);
supportRoutes.get("/tickets", getUserTickets);
supportRoutes.get("/tickets/:id", getTicketById);
supportRoutes.put("/tickets/:id", updateTicket);
supportRoutes.delete("/tickets/:id", deleteTicket);

// New chat message endpoints
supportRoutes.post("/tickets/:id/messages", sendChatMessage);
supportRoutes.get("/tickets/:id/messages", getTicketMessages);

export { supportRoutes };


