import { Hono } from "hono";
import {addconnection, getkey, linkform, disconnect,updateFormIntegration} from "../controllers/crm.controllers";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const crm = new Hono();
crm.post("/addconnection",verifySupabaseAuth,addconnection)
crm.get("/getkey/:credential_id",verifySupabaseAuth,getkey)
crm.post("/linkform",verifySupabaseAuth,linkform)
crm.post("/disconnect",verifySupabaseAuth,disconnect)
crm.put("/updateformintegration",verifySupabaseAuth,updateFormIntegration)
export { crm };
