// Support ticket interfaces

export interface CreateTicketRequest {
  subject: string;
  description: string;
  category: string;
  sub_category: string;
  video_link?: string;
  image_links?: string[];
  voice_link?: string;
}

export interface UpdateTicketRequest {
  subject?: string;
  description?: string;
  priority?: string;
}

export interface SupportTicket {
  id: number;
  created_at: string;
  subject: string;
  description: string;
  category: string;
  sub_category: string;
  image_links: string[] | null;
  video_link: string | null;
  voice_link: string;
  status: string;
  updated_at: string | null;
  created_by: string;
  assigned_to_email: string | null;
  priority: string;
  referred_to: string;
}

export interface TicketResponse extends SupportTicket {
  ticket_id: string;
  estimated_response_time: string;
  created_by_name?: string;
}

export interface TicketFilters {
  status?: string;
  category?: string;
  priority?: string;
  page?: number;
  limit?: number;
}

export interface TicketPagination {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
}



export interface TicketListResponse {
  tickets: TicketResponse[];
  pagination: TicketPagination;
  filters: {
    available_statuses: string[];
    available_categories: string[];
    available_priorities: string[];
  };
}

