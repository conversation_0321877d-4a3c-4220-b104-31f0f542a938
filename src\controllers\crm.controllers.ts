import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
const addconnection = async (c: Context) => {
    try {
        const user = c.get("user");
        if (!user) return sendApiError(c, "Unauthorized", 401);

        const userId = user.user.id;
        const { integration_id, key, name } = await c.req.json();
        if (!integration_id || !name || !key) {
            return sendApiError(c, "Missing required parameters", 400);
        }

        // Fetch user profile to get workspace_id
        const { data: userProfile, error: userProfileError } = await supabase
            .from("user_profile")
            .select("id, workspace_id")
            .eq("id", userId)
            .single();

        if (userProfileError || !userProfile) {
            console.error("User fetch error:", userProfileError);
            return sendApiError(c, "User not found", 404);
        }

        // Validate integration existence
        const { data: integration, error: integrationError } = await supabase
            .from("automate_form_integrations")
            .select("id")
            .eq("id", integration_id)
            .single();

        if (integrationError || !integration) {
            console.error("Integration Error:", integrationError);
            return sendApiError(c, "Invalid integration ID", 400);
        }

        // Insert credentials into integration_credentials
        const { data: inserted, error: insertError } = await supabase
            .from("automate_form_integration_credentials")
            .insert({
                name,
                created_by: userId,
                workspace_id: userProfile.workspace_id || null,
                integration_id,
                auth_type: "key",
                auth_data: { key },
                created_at: new Date().toISOString(),
            })
            .select("id")
            .single();

        if (insertError || !inserted) {
            console.error("Insert Error:", insertError);
            return sendApiError(c, "Failed to store credentials", 500);
        }

        return sendApiResponse(c, inserted, 201);

    } catch (err) {
        console.error("AddConnection Error:", err);
        return sendApiError(c, "Internal server error", 500);
    }
};
const getkey = async (c: Context) => {
    try {
        const user = c.get("user");
        if (!user) return sendApiError(c, "Unauthorized", 401);

        const credentialId = c.req.param("credential_id");
        if (!credentialId) return sendApiError(c, "Missing credential ID", 400);

        const { data: credential, error } = await supabase
            .from("automate_form_integration_credentials")
            .select("id, auth_data")
            .eq("id", credentialId)
            .single();

        if (error || !credential) {
            console.error("Credential fetch error:", error);
            return sendApiError(c, "Credential not found", 404);
        }

        const key = credential.auth_data?.key;
        if (!key) return sendApiError(c, "Key not found in credentials", 404);

        return sendApiResponse(c, { key }, 200);

    } catch (err) {
        console.error("GetKey Error:", err);
        return sendApiError(c, "Internal server error", 500);
    }
};
const linkform = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    let body;
    try {
      body = await c.req.json();
    } catch {
      return sendApiError(c, "Invalid JSON body", 400);
    }

    const { form_id, integration_id, credential_id, action_id, pipelineId, Source, Stage, assignedTo, column_mapped_data } = body;

    if (!form_id || !integration_id || !credential_id || !action_id || !pipelineId || !Source || !Stage || !assignedTo) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    const userId = user.user.id;

    // Check if integration already exists
    const { data: existingIntegration, error: checkError } = await supabase
      .from("form_integrations")
      .select("id")
      .eq("form_id", form_id)
      .eq("integration_id", integration_id)
      .eq("credential_id", credential_id)
      .single();

    const metadata = {
      pipelineId,
      Source,
      Stage,
      assignedTo,
    };

    if (existingIntegration) {
      // Update existing integration
      const { error: updateError } = await supabase
        .from("form_integrations")
        .update({
          action_id,
          metadata,
          mapped_data: column_mapped_data || null,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingIntegration.id);

      if (updateError) {
        console.error("DB Update Error:", updateError);
        return sendApiError(c, "Failed to update form integration", 500);
      }

      return sendApiResponse(c, { message: "Form integration updated successfully" }, 200);
    }

    // Insert new integration if it doesn't exist
    const { data:linkdata,error } = await supabase
      .from("form_integrations")
      .insert({
        form_id,
        integration_id,
        credential_id,
        action_id,
        created_by: userId,
        metadata,
        mapped_data: column_mapped_data || null
      })
      .select("id")
      .single();

    if (error) {
      console.error("DB Insert Error:", error);
      return sendApiError(c, "Failed to save form integration", 500);
    }

    return sendApiResponse(c, { message: "Form integration linked successfully",data:linkdata.id }, 200);
  } catch (err) {
    console.error("Linkform Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const updateFormIntegration = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    let body;
    try {
      body = await c.req.json();
    } catch {
      return sendApiError(c, "Invalid JSON body", 400);
    }

    const { 
      form_integration_id,  // ID of the existing integration
      action_id, 
      pipelineId, 
      Source, 
      Stage, 
      assignedTo, 
      column_mapped_data 
    } = body;

    if (!form_integration_id) {
      return sendApiError(c, "Integration ID is required", 400);
    }

    // Verify integration exists and user has permission
    const { data: existingIntegration, error: checkError } = await supabase
      .from("form_integrations")
      .select("id, created_by, form_id, integration_id, credential_id, metadata")
      .eq("id", form_integration_id)
      .single();

    if (checkError || !existingIntegration) {
      return sendApiError(c, "Form integration not found", 404);
    }

    if (existingIntegration.created_by !== user.user.id) {
      return sendApiError(c, "You don't have permission to update this integration", 403);
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Only update fields that are provided
    if (action_id) updateData.action_id = action_id;
    if (column_mapped_data) updateData.mapped_data = column_mapped_data;
    if (pipelineId || Source || Stage || assignedTo) {
      updateData.metadata = {
        ...(existingIntegration.metadata as Record<string, any> || {}),  // Preserve existing metadata
        ...(pipelineId && { pipelineId }),
        ...(Source && { Source }),
        ...(Stage && { Stage }),
        ...(assignedTo && { assignedTo })
      };
    }

    // Update the integration
    const { data: updated, error: updateError } = await supabase
      .from("form_integrations")
      .update(updateData)
      .eq("id", form_integration_id)
      .select("*")
      .single();

    if (updateError) {
      console.error("Update Error:", updateError);
      return sendApiError(c, "Failed to update form integration", 500);
    }

    return sendApiResponse(c, {
      message: "Form integration updated successfully",
      data: {
        integration_id: updated.id,
        form_id: updated.form_id,
        action_id: updated.action_id,
        metadata: updated.metadata,
        mapped_data: updated.mapped_data,
        updated_at: updated.updated_at
      }
    }, 200);

  } catch (err) {
    console.error("UpdateFormIntegration Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const disconnect = async (c: Context) => {
    try {
        const user = c.get("user");
        if (!user) return sendApiError(c, "Unauthorized", 401);

        const { form_id, credential_id } = await c.req.json();
        if (!credential_id || !form_id) {
            return sendApiError(c, "Form ID and Credential ID are required", 400);
        }

        // First, check if the form integration exists and belongs to the user
        const { data: formIntegration, error: checkError } = await supabase
            .from("form_integrations")
            .select("id, created_by")
            .eq("form_id", form_id)
            .eq("credential_id", credential_id)
            .single();

        if (checkError || !formIntegration) {
            return sendApiError(c, "Form integration not found", 404);
        }

        if (formIntegration.created_by !== user.user.id) {
            return sendApiError(c, "You don't have permission to disconnect this integration", 403);
        }

        // Delete only this specific form integration
        const { error: deleteError } = await supabase
            .from("form_integrations")
            .delete()
            .eq("form_id", form_id)
            .eq("credential_id", credential_id);

        if (deleteError) {
            console.error("Form Integration Delete Error:", deleteError);
            return sendApiError(c, "Failed to disconnect form integration", 500);
        }

        return sendApiResponse(c, { 
            message: "Form integration disconnected successfully" 
        }, 200);

    } catch (err) {
        console.error("Disconnect Error:", err);
        return sendApiError(c, "Internal server error", 500);
    }
};


export { addconnection, getkey, linkform, updateFormIntegration, disconnect };
