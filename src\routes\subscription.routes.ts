import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import {
  getWorkspaceSubscriptions,
  renewSubscription,
  purchaseSubscription

} from "../controllers/subscription.controllers";

const subscriptionRoutes = new Hono();

// Purchase a subscription
subscriptionRoutes.post("/purchase", verifySupabaseAuth, purchaseSubscription);

// Get workspace subscriptions
subscriptionRoutes.get("/", verifySupabaseAuth, getWorkspaceSubscriptions);

// Manually renew a subscription
subscriptionRoutes.post("/renew/:subscription_id", verifySupabaseAuth, renewSubscription);




export { subscriptionRoutes };
