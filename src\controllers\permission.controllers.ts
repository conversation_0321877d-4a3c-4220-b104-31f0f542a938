import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";


const getAllPermissions = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const roleId = c.req.param("id");
    if (!roleId) {
      return sendApiError(c, "Role ID is required", 400);
    }

    // First, check if the role exists and user has access
    const { data: existingRole, error: roleError } = await supabase
      .from("automateform_role")
      .select("id, name, workspace_id")
      .eq("id", roleId)
      .single();

    if (roleError || !existingRole) {
      return sendApiError(c, "Role not found", 404);
    }

    // Get all permissions with assigned status using our custom function
    const { data: permissions, error: permissionsError } = await supabase.rpc(
      'get_role_permissions_by_type',
      { p_role_id: roleId }
    );

    if (permissionsError) {
      console.error("Get Role Permissions Error:", permissionsError);
      return sendApiError(c, "Failed to retrieve role permissions", 500);
    }

    return sendApiResponse(c, {
      message: "Role permissions retrieved successfully",
      // role: existingRole,
      data: permissions || []
    });
  } catch (err) {
    console.error("Get Role Permissions Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Create a new permission
const createPermission = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    // Check if user is a system admin (you may need to adjust this based on your system)
    const { data: userProfile, error: userError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    if (userError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    if (userProfile.role !== "admin") {
      return sendApiError(c, "Only system administrators can create permissions", 403);
    }

    const body = await c.req.json();
    const { code, description } = body;

    if (!code) {
      return sendApiError(c, "Permission code is required", 400);
    }

    const { data, error } = await supabase
      .from("automateform_permission")
      .insert([{ code, description }])
      .select()
      .single();

    if (error) {
      if (error.code === "23505") {
        return sendApiError(c, "Permission with this code already exists", 409);
      }
      console.error("Create Permission Error:", error);
      return sendApiError(c, "Failed to create permission", 500);
    }

    return sendApiResponse(c, { 
      message: "Permission created successfully", 
      permission: data 
    }, 201);
  } catch (err) {
    console.error("Create Permission Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Update a permission
const updatePermission = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    // Check if user is a system admin
    const { data: userProfile, error: userError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    if (userError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    if (userProfile.role !== "admin") {
      return sendApiError(c, "Only system administrators can update permissions", 403);
    }

    const permissionId = c.req.param("id");
    if (!permissionId) {
      return sendApiError(c, "Permission ID is required", 400);
    }

    const body = await c.req.json();
    const { description } = body;

    // Note: We don't allow updating the code as it might be referenced elsewhere
    const { data, error } = await supabase
      .from("automateform_permission")
      .update({ description })
      .eq("id", permissionId)
      .select()
      .single();

    if (error) {
      console.error("Update Permission Error:", error);
      return sendApiError(c, "Failed to update permission", 500);
    }

    if (!data) {
      return sendApiError(c, "Permission not found", 404);
    }

    return sendApiResponse(c, { 
      message: "Permission updated successfully", 
      permission: data 
    });
  } catch (err) {
    console.error("Update Permission Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Delete a permission
const deletePermission = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    // Check if user is a system admin
    const { data: userProfile, error: userError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    if (userError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    if (userProfile.role !== "admin") {
      return sendApiError(c, "Only system administrators can delete permissions", 403);
    }

    const permissionId = c.req.param("id");
    if (!permissionId) {
      return sendApiError(c, "Permission ID is required", 400);
    }

    // Check if permission is being used by any roles
    const { data: usedPermissions, error: checkError } = await supabase
      .from("automateform_role_permission")
      .select("id")
      .eq("permission_id", permissionId)
      .limit(1);

    if (checkError) {
      console.error("Check Permission Usage Error:", checkError);
      return sendApiError(c, "Failed to check if permission is in use", 500);
    }

    if (usedPermissions && usedPermissions.length > 0) {
      return sendApiError(c, "Cannot delete permission as it is assigned to one or more roles", 409);
    }

    const { error } = await supabase
      .from("automateform_permission")
      .delete()
      .eq("id", permissionId);

    if (error) {
      console.error("Delete Permission Error:", error);
      return sendApiError(c, "Failed to delete permission", 500);
    }

    return sendApiResponse(c, { 
      message: "Permission deleted successfully" 
    });
  } catch (err) {
    console.error("Delete Permission Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { getAllPermissions, createPermission, updatePermission, deletePermission };

