import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { shareForm, getSharedForms, removeFormAccess, getFormAccessUsers } from "../controllers/formShare.controllers";

const formShare = new Hono();

// Share a form with a user
formShare.post("/", verifySupabaseAuth, shareForm);

// Get forms shared with the current user
formShare.get("/", verifySupabaseAuth, getSharedForms);

// Remove form access for a user
formShare.delete("/:id", verifySupabaseAuth, removeFormAccess);

// Get users with access to a form
formShare.get("/users/:form_id", verifySupabaseAuth, getFormAccessUsers);

export { formShare };