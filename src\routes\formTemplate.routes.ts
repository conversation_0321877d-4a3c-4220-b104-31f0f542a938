import { Hono } from "hono";
import { getCategories,getTemplatesByCategory,createFormTemplate,createCategory,updateCategory,updateFormTemplate,deleteFormTemplate ,deleteCategory,getTemplateById, toggleTemplateStatus, cloneTemplate} from "../controllers/formTemplate.controllers";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const formTemplate = new Hono();
formTemplate.get("/categories", verifySupabaseAuth, getCategories);
formTemplate.post("/categories", verifySupabaseAuth, createCategory);
formTemplate.put("/categories/:id", verifySupabaseAuth, updateCategory);
formTemplate.delete("/categories/:id", verifySupabaseAuth,deleteCategory );

formTemplate.post("/", verifySupabaseAuth,createFormTemplate );
formTemplate.get("/:categoryId", verifySupabaseAuth, getTemplatesByCategory);
formTemplate.get("/temp/:templateId", verifySupabaseAuth, getTemplateById);
formTemplate.put("/:templateId", verifySupabaseAuth, updateFormTemplate);
formTemplate.delete("/:templateId", verifySupabaseAuth, deleteFormTemplate);
formTemplate.put("/status/:templateId", verifySupabaseAuth, toggleTemplateStatus);
formTemplate.post("/clone/:templateId", verifySupabaseAuth, cloneTemplate);
export { formTemplate};
