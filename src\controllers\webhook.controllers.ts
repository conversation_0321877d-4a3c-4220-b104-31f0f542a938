import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { google } from "googleapis";
import { getValidGoogleToken } from "../utils/GoogleToken";
import { mergeFieldsWithAnswers } from "../utils/comman";
import axios from "axios";
const CRM_API_URL = "https://api.automatebusiness.com/functions/v1/createLead";

type FormField = {
  id: any;
  name: any;
  type: any;
  component: any;
  value: any;
  [key: string]: any; // Index Signature for dynamic properties
};

// Type definitions
interface WebhookLogEntry {
  id: number;
}

interface FormRecord {
  id: string;
  form_id: string;
  answers: Answer[];
}

interface Answer {
  id: string;
  value: string | NameValue | null;
}

interface NameValue {
  firstName: string;
  lastName: string;
}

interface FormFields {
  fields: any[];
}

interface FormIntegration {
  metadata: IntegrationMetadata;
  credential_id: string;
  mapped_data: MappedData[];
}

interface IntegrationMetadata {
  category_id: number;
  Source: string;
  Stage: string;
  assignedTo: string;
  pipelineId: string;
}

interface MappedData {
  id: string;
  name: string;
}

const sendDataToGoogleSheet = async (c: Context) => {
  let logEntry: { id: number } | null = null;

  try {
    // 1) Parse the request body
    const body = await c.req.json();

    if (!body?.record || !body.record.form_id || !body.record.answers) {
      throw new Error("Invalid request payload: Missing form_id or answers");
    }

    const { form_id, answers } = body.record;
    console.log("🚀 Submitting Form to Google Sheets...,answers:", answers);

    // 2) Insert a row in webhook_execution_logs to track this request
    const { data: logData, error: logError } = await supabase
      .from("automate_form_webhook_execution_logs")
      .insert({
        response_id: body.record.id,
        data_sent: body.record,
        status: "pending",
      })
      .select("id")
      .single();

    if (logError) {
      console.error(
        "Failed to create webhook execution log:",
        logError.message
      );
    } else {
      logEntry = logData; 
    }

    // 3) Fetch Google Sheets integration for the form
    const GOOGLE_SHEET_INTEGRATION_ID = "96e706d1-a0b6-491c-9eb2-fc97603edcab";
    const { data: formIntegrations, error: formIntegrationError } =
      await supabase
        .from("form_integrations")
        .select("metadata, credential_id, enabled")
        .eq("form_id", form_id)
        .eq("integration_id", GOOGLE_SHEET_INTEGRATION_ID)
        .single();

    if (formIntegrationError || !formIntegrations) {
      console.error(
        "No Google Sheets integration found:",
        formIntegrationError?.message || "No integrations available"
      );
      return sendApiError(c, "Google Sheets integration not configured", 400);
    }

    // Check if the integration is enabled
    if (!formIntegrations.enabled) {
      console.error("Google Sheets integration is disabled");
      return sendApiError(c, "Google Sheets integration is disabled", 400);
    }

    // Process the integration
    const spreadsheetId = formIntegrations.metadata?.spreadsheetId;
    const credentialId = formIntegrations.credential_id;

    if (!spreadsheetId) {
      console.error("No spreadsheet linked for this integration.");
      return sendApiError(c, "No spreadsheet configured for this integration", 400);
    }

    console.log(
      `📄 Processing Google Sheet Integration for: ${spreadsheetId}`
    );

    // Get valid Google access token for this credential
    const accessToken: any = await getValidGoogleToken(credentialId);
    if (!accessToken) {
      console.error(
        `Failed to retrieve Google access token for credential: ${credentialId}`
      );
      return sendApiError(c, "Failed to authenticate with Google", 401);
    }

    // Configure the Google Sheets client
    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({ access_token: accessToken });
    const sheets = google.sheets({ version: "v4", auth: oauth2Client });

    // Retrieve existing column headers (first row)
    const sheetResponse = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: "Sheet1!A1:Z1",
    });

    let existingHeaders = sheetResponse.data.values?.[0] || [];
    console.log("📑 Existing Headers:", existingHeaders);

    // Prepare new headers and row data
    let newHeaders = [...existingHeaders];
    let rowData: any[] = [];

    // Use the title field as the header and value as the data
    answers.forEach((answer:any) => {
      if (typeof answer.value === "object" && answer.value !== null) {
        // Handle name fields with firstName and lastName
        if ('firstName' in answer.value && 'lastName' in answer.value) {
          // First Name
          const firstNameTitle = answer.firstNameTitle || "First Name";
          if (!newHeaders.includes(firstNameTitle)) {
            newHeaders.push(firstNameTitle);
          }
          rowData[newHeaders.indexOf(firstNameTitle)] = answer.value.firstName;
          
          // Last Name
          const lastNameTitle = answer.lastNameTitle || "Last Name";
          if (!newHeaders.includes(lastNameTitle)) {
            newHeaders.push(lastNameTitle);
          }
          rowData[newHeaders.indexOf(lastNameTitle)] = answer.value.lastName;
        } else {
          // Handle other object values
          Object.entries(answer.value).forEach(([key, value]) => {
            const columnTitle = answer[`${key}Title`] || key;
            if (!newHeaders.includes(columnTitle)) {
              newHeaders.push(columnTitle);
            }
            rowData[newHeaders.indexOf(columnTitle)] = value;
          });
        }
      } else {
        // Use title as the header if available, otherwise use name
        const headerName = answer.title || answer.name;
        if (!newHeaders.includes(headerName)) {
          newHeaders.push(headerName);
        }
        rowData[newHeaders.indexOf(headerName)] = answer.value;
      }
    });

    // Add Submitted At column
    if (!newHeaders.includes("Submitted At")) {
      newHeaders.push("Submitted At");
    }
    rowData[newHeaders.indexOf("Submitted At")] =
      body.record.submitted_at || new Date().toISOString();

    console.log("🆕 New Headers:", newHeaders);
    console.log("📝 Row Data:", rowData);

    // Update the sheet headers if there are any new ones
    if (newHeaders.length > existingHeaders.length) {
      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: "Sheet1!A1:Z1",
        valueInputOption: "RAW",
        requestBody: {
          values: [newHeaders],
        },
      });
      console.log("✅ Updated Headers in Google Sheets");
    }

    // Append the new row of data
    await sheets.spreadsheets.values.append({
      spreadsheetId,
      range: "Sheet1!A2:Z",
      valueInputOption: "RAW",
      requestBody: {
        values: [rowData],
      },
    });
    console.log("✅ Data appended to Google Sheets");

    // If everything goes well, update webhook_execution_logs with a success message and status
    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: "Successfully submitted to Google Sheets",
          status: "success",
        })
        .eq("id", logEntry.id);
    }

    // Send a success response back to the caller
    return sendApiResponse(c, {
      message: "Successfully submitted form response to Google Sheets",
      data: body,
    });
  } catch (err) {
    // Log the error in the database if a log entry was created
    console.error("Submit Form Data Error in GoogleSheet:", err);
    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: `Error: ${err}`,
          status: "error",
        })
        .eq("id", logEntry.id);
    }

    // Return an error response
    return sendApiError(c, "Internal server error", 500);
  }
};
const createFormWebhook = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { form_id, webhook_url, integration_id } = body;

    if (!form_id || !webhook_url) {
      return sendApiError(c, "Form ID and webhook URL are required", 400);
    }

    // Verify the form belongs to the user's workspace
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("workspace_id")
      .eq("id", form_id)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    // Get user's workspace_id
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    // Verify user has access to this form
    if (form.workspace_id !== userProfile.workspace_id) {
      return sendApiError(c, "You don't have access to this form", 403);
    }

    // Create the webhook
    const { data: webhook, error: webhookError } = await supabase
      .from("automate_forms_webhook")
      .insert({
        form_id,
        webhook_url,
        workspace_id: form.workspace_id,
        integration_id: integration_id || null,
        status: true // Enable by default
      })
      .select()
      .single();

    if (webhookError) {
      console.error("Create Webhook Error:", webhookError);
      return sendApiError(c, "Failed to create webhook", 500);
    }

    return sendApiResponse(c, webhook, 201);
  } catch (err) {
    console.error("Create Webhook Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


export { sendDataToGoogleSheet };
