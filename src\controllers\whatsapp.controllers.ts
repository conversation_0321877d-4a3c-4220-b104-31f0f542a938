import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

const addconnection = async (c: Context) => {
    try {
        const user = c.get("user");
        if (!user) return sendApiError(c, "Unauthorized", 401);

        const userId = user.user.id;
        const { integration_id, key, name ,phone_number_id,waba_id,wa_channel_id} = await c.req.json();
        if (!integration_id || !name || !key) {
            return sendApiError(c, "Missing required parameters", 400);
        }

        // Fetch user profile to get workspace_id
        const { data: userProfile, error: userProfileError } = await supabase
            .from("user_profile")
            .select("id, workspace_id")
            .eq("id", userId)
            .single();

        if (userProfileError || !userProfile) {
            console.error("User fetch error:", userProfileError);
            return sendApiError(c, "User not found", 404);
        }

        // Validate integration existence
        const { data: integration, error: integrationError } = await supabase
            .from("automate_form_integrations")
            .select("id")
            .eq("id", integration_id)
            .single();

        if (integrationError || !integration) {
            console.error("Integration Error:", integrationError);
            return sendApiError(c, "Invalid integration ID", 400);
        }

        // Insert credentials into integration_credentials
        const { data: inserted, error: insertError } = await supabase
            .from("automate_form_integration_credentials")
            .insert({
                name,
                created_by: userId,
                workspace_id: userProfile.workspace_id || null,
                integration_id,
                auth_type: "key",
                auth_data: { key,phone_number_id,waba_id,wa_channel_id },
                created_at: new Date().toISOString(),
            })
            .select("id")
            .single();

        if (insertError || !inserted) {
            console.error("Insert Error:", insertError);
            return sendApiError(c, "Failed to store credentials", 500);
        }

        return sendApiResponse(c, inserted, 201);

    } catch (err) {
        console.error("AddConnection Error:", err);
        return sendApiError(c, "Internal server error", 500);
    }
};

const updateconnection = async (c: Context) => {
    try {
        const user = c.get("user");
        if (!user) return sendApiError(c, "Unauthorized", 401);

        const userId = user.user.id;
        const { credential_id, key, name, phone_number_id, waba_id, wa_channel_id } = await c.req.json();

        if (!credential_id || !name || !key) {
            return sendApiError(c, "Missing required parameters", 400);
        }

        // Verify the credential exists and belongs to the user
        const { data: credential, error: credentialError } = await supabase
            .from("automate_form_integration_credentials")
            .select("id, created_by")
            .eq("id", credential_id)
            .single();

        if (credentialError || !credential) {
            console.error("Credential fetch error:", credentialError);
            return sendApiError(c, "Credential not found", 404);
        }

        // Check if the user has permission to update this credential
        if (credential.created_by !== userId) {
            return sendApiError(c, "You don't have permission to update this connection", 403);
        }

        // Update the credential
        const { data: updated, error: updateError } = await supabase
            .from("automate_form_integration_credentials")
            .update({
                name,
                auth_data: { key, phone_number_id, waba_id, wa_channel_id },
                updated_at: new Date().toISOString(),
            })
            .eq("id", credential_id)
            .select("id")
            .single();

        if (updateError || !updated) {
            console.error("Update Error:", updateError);
            return sendApiError(c, "Failed to update credentials", 500);
        }

        return sendApiResponse(c, updated, 200);

    } catch (err) {
        console.error("UpdateConnection Error:", err);
        return sendApiError(c, "Internal server error", 500);
    }
};
const getTemplate = async (c: Context) => {
    try {
        const user = c.get("user");
        if (!user) return sendApiError(c, "Unauthorized", 401);

        const userId = user.user.id;
        const { credential_id } = await c.req.json();

        if (!credential_id) {
            return sendApiError(c, "Missing required parameters", 400);
        }

        // Verify the credential exists and belongs to the user
        const { data: credential, error: credentialError } = await supabase
            .from("automate_form_integration_credentials")
            .select("id, created_by, auth_data")
            .eq("id", credential_id)
            .single();

        if (credentialError || !credential) {
            console.error("Credential fetch error:", credentialError);
            return sendApiError(c, "Credential not found", 404);
        }

        // Check if the user has permission to access this credential
        if (credential.created_by !== userId) {
            return sendApiError(c, "You don't have permission to access this connection", 403);
        }

        // Extract WhatsApp Business Account ID and token from auth_data
        const { key: token, waba_id } = credential.auth_data;
        
        if (!token || !waba_id) {
            return sendApiError(c, "Missing WhatsApp credentials", 400);
        }

        // Set up the API endpoint
        const domain = "https://crmapi.automatebusiness.com/api/meta";
        const version = "v19.0";
        const limit = 10;
        const offset = 0;
        
        const endPoint = `${domain}/${version}/${waba_id}/message_templates?limit=${limit}&offset=${offset}`;

        // Make the API request
        const response = await fetch(endPoint, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${token}`,
                "Content-Type": "application/json"
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Template fetch error:", response.status, errorText);
            return sendApiError(c, `Failed to fetch templates: ${response.statusText}`, response.status);
        }

        const templates = await response.json();
        return sendApiResponse(c, templates, 200);

    } catch (err) {
        console.error("GetTemplate Error:", err);
        return sendApiError(c, "Internal server error", 500);
    }
};

const linkForm = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) return sendApiError(c, "Unauthorized", 401);
  
      let body;
      try {
        body = await c.req.json();
      } catch {
        return sendApiError(c, "Invalid JSON body", 400);
      }
  
      const { form_id, integration_id, credential_id, action_id, column_mapped_data,template_id,template_name } = body;
  
      if (!form_id || !integration_id || !credential_id || !action_id || !column_mapped_data || !template_id) {
        return sendApiError(c, "Missing required parameters", 400);
      }
  
      const userId = user.user.id;
  
      // Check if integration already exists
      const { data: existingIntegration, error: checkError } = await supabase
        .from("form_integrations")
        .select("id")
        .eq("form_id", form_id)
        .eq("integration_id", integration_id)
        .eq("credential_id", credential_id)
        .single();

      
  
      if (existingIntegration) {
        // Update existing integration
        const { error: updateError } = await supabase
          .from("form_integrations")
          .update({
            action_id,
            mapped_data: column_mapped_data || null,
            updated_at: new Date().toISOString()
          })
          .eq("id", existingIntegration.id);
  
        if (updateError) {
          console.error("DB Update Error:", updateError);
          return sendApiError(c, "Failed to update form integration", 500);
        }
  
        return sendApiResponse(c, { message: "Form integration updated successfully" }, 200);
      }
  
      // Insert new integration if it doesn't exist
      const { error } = await supabase
        .from("form_integrations")
        .insert({
          form_id,
          integration_id,
          credential_id,
          action_id,
          created_by: userId,
          metadata:{template_id,template_name},
          mapped_data: column_mapped_data || null
        });
  
      if (error) {
        console.error("DB Insert Error:", error);
        return sendApiError(c, "Failed to save form integration", 500);
      }
  
      return sendApiResponse(c, { message: "Form integration linked successfully" }, 200);
    } catch (err) {
      console.error("Linkform Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };

export { addconnection, updateconnection,getTemplate,linkForm };
