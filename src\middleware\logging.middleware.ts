import type { Context } from "hono";
import { supabase } from "../db";

interface RequestData {
  method: string;
  path: string;
  query: Record<string, string>;
  headers: Record<string, string>;
  body: any;
}

interface LogEntry {
  id: number;
  request_id: string;
  user_id?: string;
  method: string;
  path: string;
  request_data: RequestData;
  status: 'pending' | 'success' | 'error';
  error_details?: string;
  response_time?: number;
  started_at: string;
  completed_at?: string;
}

interface User {
  user: {
    id: string;
  };
}

export const apiLogger = async (c: Context, next: () => Promise<void>) => {
  const requestStart = Date.now();
  const requestId = crypto.randomUUID();
  
  // Clone the request for body parsing
  const clonedReq = c.req.raw.clone();
  
  // Get all headers
  const headers: Record<string, string> = {};
  c.req.raw.headers.forEach((value, key) => {
    headers[key] = value;
  });
  
  // Capture request details
  const requestData: RequestData = {
    method: c.req.method,
    path: new URL(c.req.url).pathname,
    query: Object.fromEntries(new URL(c.req.url).searchParams),
    headers,
    body: null
  };

  // Only parse JSON body if content-type is application/json
  if (c.req.header('content-type')?.includes('application/json')) {
    try {
      requestData.body = await clonedReq.json();
    } catch (e) {
      // Ignore body parse errors
    }
  }

  try {
    // Get user information if available
    const user = c.get('user') as User | undefined;
    
    // Create initial log entry
    const { data: logEntry, error: logError } = await supabase
      .from('automate_form_api_logs')
      .insert({
        request_id: requestId,
        user_id: user?.user?.id,
        method: requestData.method,
        path: requestData.path,
        request_data: requestData,
        status: 'pending' as const,
        started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (logError) {
      console.error('Failed to create log entry:', logError);
    }

    // Store the log entry ID in context for later use
    if (logEntry) {
      c.set('logEntry', logEntry as LogEntry);
    }
    
    // Execute the next middleware/route handler
    await next();

    // Calculate response time
    const responseTime = Date.now() - requestStart;

    // Update log entry with response data
    if (logEntry) {
      await supabase
        .from('automate_form_api_logs')
        .update({
          status: 'success' as const,
          response_time: responseTime,
          completed_at: new Date().toISOString()
        })
        .eq('id', logEntry.id);
    }

  } catch (error) {
    // Log error details
    const responseTime = Date.now() - requestStart;
    const logEntry = c.get('logEntry') as LogEntry | undefined;

    if (logEntry) {
      await supabase
        .from('automate_form_api_logs')
        .update({
          status: 'error' as const,
          error_details: error instanceof Error ? error.message : 'Unknown error',
          response_time: responseTime,
          completed_at: new Date().toISOString()
        })
        .eq('id', logEntry.id);
    }

    throw error;
  }
};

