import { supabase } from "../db";

/**
 * Service for tracking and managing workspace usage
 */
export class UsageService {
  /**
   * Updates the storage usage for a workspace
   * @param workspaceId The workspace ID
   * @param fileSizeMB The file size in MB to add to the current usage
   */
  static async updateStorageUsage(workspaceId: number, fileSizeMB: number): Promise<boolean> {
    try {
      // Get current usage
      const { data: currentUsage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("id, storage_used_mb")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return false;
      }

      // Update storage usage
      const { error: updateError } = await supabase
        .from("automate_form_workspace_usage")
        .update({
          storage_used_mb: (currentUsage?.storage_used_mb || 0) + fileSizeMB,
          updated_at: new Date().toISOString()
        })
        .eq("id", currentUsage?.id);

      if (updateError) {
        console.error("Error updating storage usage:", updateError);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Unexpected error in updateStorageUsage:", err);
      return false;
    }
  }

  /**
   * Decrements the storage usage for a workspace
   * @param workspaceId The workspace ID
   * @param fileSizeMB The file size in MB to subtract from the current usage
   */
  static async decrementStorageUsage(workspaceId: number, fileSizeMB: number): Promise<boolean> {
    try {
      // Get current usage
      const { data: currentUsage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("id, storage_used_mb")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return false;
      }

      // Calculate new usage (ensure it doesn't go below 0)
      const newUsage = Math.max(0, (currentUsage?.storage_used_mb || 0) - fileSizeMB);

      // Update storage usage
      const { error: updateError } = await supabase
        .from("automate_form_workspace_usage")
        .update({
          storage_used_mb: newUsage,
          updated_at: new Date().toISOString()
        })
        .eq("id", currentUsage?.id);

      if (updateError) {
        console.error("Error updating storage usage:", updateError);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Unexpected error in decrementStorageUsage:", err);
      return false;
    }
  }

  /**
   * Increments the submission count for a workspace
   * @param workspaceId The workspace ID
   */
  static async incrementSubmissionCount(workspaceId: number): Promise<boolean> {
    try {
      // Get current usage
      const { data: currentUsage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("id, submissions_count")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return false;
      }

      // Update submission count
      const { error: updateError } = await supabase
        .from("automate_form_workspace_usage")
        .update({
          submissions_count: (currentUsage?.submissions_count || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq("id", currentUsage?.id);

      if (updateError) {
        console.error("Error updating submission count:", updateError);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Unexpected error in incrementSubmissionCount:", err);
      return false;
    }
  }

  /**
   * Increments the form creation count for a workspace
   * @param workspaceId The workspace ID
   */
  static async incrementFormCreationCount(workspaceId: number): Promise<boolean> {
    try {
      // Get current usage
      const { data: currentUsage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("id, create_form_count")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return false;
      }

      // Update form creation count
      const { error: updateError } = await supabase
        .from("automate_form_workspace_usage")
        .update({
          create_form_count: (currentUsage?.create_form_count || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq("id", currentUsage?.id);

      if (updateError) {
        console.error("Error updating form creation count:", updateError);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Unexpected error in incrementFormCreationCount:", err);
      return false;
    }
  }

  /**
   * Decrements the form creation count for a workspace
   * @param workspaceId The workspace ID
   */
  static async decrementFormCreationCount(workspaceId: number): Promise<boolean> {
    try {
      // Get current usage
      const { data: currentUsage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("id, create_form_count")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return false;
      }

      // Calculate new count (ensure it doesn't go below 0)
      const newCount = Math.max(0, (currentUsage?.create_form_count || 0) - 1);

      // Update form creation count
      const { error: updateError } = await supabase
        .from("automate_form_workspace_usage")
        .update({
          create_form_count: newCount,
          updated_at: new Date().toISOString()
        })
        .eq("id", currentUsage?.id);

      if (updateError) {
        console.error("Error updating form creation count:", updateError);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Unexpected error in decrementFormCreationCount:", err);
      return false;
    }
  }

  /**
   * Updates the AI credits usage for a workspace
   * @param workspaceId The workspace ID
   * @param creditsUsed The number of AI credits used
   */
  static async updateAICreditsUsage(workspaceId: number, creditsUsed: number): Promise<boolean> {
    try {
      // Get current usage
      const { data: currentUsage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("id, ai_credits_used")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return false;
      }

      // Update AI credits usage
      const { error: updateError } = await supabase
        .from("automate_form_workspace_usage")
        .update({
          ai_credits_used: (currentUsage?.ai_credits_used || 0) + creditsUsed,
          updated_at: new Date().toISOString()
        })
        .eq("id", currentUsage?.id);

      if (updateError) {
        console.error("Error updating AI credits usage:", updateError);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Unexpected error in updateAICreditsUsage:", err);
      return false;
    }
  }

  /**
   * Updates the team member count for a workspace
   * @param workspaceId The workspace ID
   */
  static async updateTeamMemberCount(workspaceId: number): Promise<boolean> {
    try {
      // Get current member count
      const { count: memberCount, error: countError } = await supabase
        .from("workspace_members")
        .select("*", { count: "exact" })
        .eq("workspace_id", workspaceId);

      if (countError) {
        console.error("Error counting workspace members:", countError);
        return false;
      }

      // Get current usage
      const { data: currentUsage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("id")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return false;
      }

      // Update team member count
      const { error: updateError } = await supabase
        .from("automate_form_workspace_usage")
        .update({
          team_member_count: memberCount || 0,
          updated_at: new Date().toISOString()
        })
        .eq("id", currentUsage?.id);

      if (updateError) {
        console.error("Error updating team member count:", updateError);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Unexpected error in updateTeamMemberCount:", err);
      return false;
    }
  }

  /**
   * Gets the current usage for a workspace
   * @param workspaceId The workspace ID
   */
  static async getWorkspaceUsage(workspaceId: number) {
    try {
      // Get current usage
      const { data: usage, error: usageError } = await supabase
        .from("automate_form_workspace_usage")
        .select("*")
        .eq("workspace_id", workspaceId)
        .gte("billing_cycle_end", new Date().toISOString())
        .order("billing_cycle_end", { ascending: false })
        .limit(1)
        .single();

      if (usageError) {
        console.error("Error fetching workspace usage:", usageError);
        return null;
      }

      // Get usage limits
      const { data: limits, error: limitsError } = await supabase
        .from("automate_form_workspace_usage_limits")
        .select("*")
        .eq("workspace_id", workspaceId)
        .single();

      if (limitsError) {
        console.error("Error fetching workspace usage limits:", limitsError);
        return null;
      }

      return {
        usage,
        limits
      };
    } catch (err) {
      console.error("Unexpected error in getWorkspaceUsage:", err);
      return null;
    }
  }

  /**
   * Gets current usage percentage for subscription upgrade validation
   * @param workspaceId The workspace ID
   * @returns Object with usage percentages and remaining days in billing cycle
   */
  static async getCurrentUsageForUpgrade(workspaceId: number) {
    try {
      const usageData = await this.getWorkspaceUsage(workspaceId);
      if (!usageData) return null;

      // Calculate usage percentages
      const usagePercentages = {
        storage: (usageData.usage.storage_used_mb / usageData.limits.storage_limit_mb) * 100,
        submissions: (usageData.usage.submissions_count / usageData.limits.monthly_submissions_limit) * 100,
        forms: usageData.usage.create_form_count
          ? (usageData.usage.create_form_count / usageData.limits.create_form_limit) * 100
          : 0,
        teamMembers: usageData.usage.team_member_count
          ? (usageData.usage.team_member_count / usageData.limits.team_member_limit) * 100
          : 0,
        aiCredits: usageData.usage.ai_credits_used
          ? (usageData.usage.ai_credits_used / usageData.limits.ai_credits_limit) * 100
          : 0,
      };

      // Calculate days remaining in billing cycle
      const daysRemaining = Math.max(
        0,
        Math.ceil(
          (new Date(usageData.usage.billing_cycle_end).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
        )
      );

      return {
        usagePercentages,
        daysRemaining,
        currentUsage: usageData.usage,
        currentLimits: usageData.limits
      };
    } catch (err) {
      console.error("Unexpected error in getCurrentUsageForUpgrade:", err);
      return null;
    }
  }

  /**
   * Checks if a specific usage limit has been reached
   * @param workspaceId The workspace ID
   * @param limitType The type of limit to check ('storage', 'submissions', 'forms', 'teamMembers', 'aiCredits')
   * @param additionalUsage Optional additional usage to consider (e.g., for checking if an action would exceed the limit)
   * @returns Object with isLimitReached and message properties, or null if error
   */
  static async checkLimitReached(
    workspaceId: number,
    limitType: 'storage' | 'submissions' | 'forms' | 'teamMembers' | 'aiCredits',
    additionalUsage: number = 0
  ) {
    try {
      const usageData = await this.getWorkspaceUsage(workspaceId);
      if (!usageData) return null;

      let currentUsage = 0;
      let limit = 0;
      let message = '';

      switch (limitType) {
        case 'storage':
          currentUsage = usageData.usage.storage_used_mb + additionalUsage;
          limit = usageData.limits.storage_limit_mb;
          message = 'Storage limit reached. Please upgrade your plan to get more storage space.';
          break;
        case 'submissions':
          currentUsage = usageData.usage.submissions_count + additionalUsage;
          limit = usageData.limits.monthly_submissions_limit;
          message = 'Monthly submission limit reached. Please upgrade your plan to accept more submissions.';
          break;
        case 'forms':
          currentUsage = usageData.usage.create_form_count + additionalUsage;
          limit = usageData.limits.create_form_limit;
          message = 'Form creation limit reached. Please upgrade your plan to create more forms.';
          break;
        case 'teamMembers':
          currentUsage = usageData.usage.team_member_count + additionalUsage;
          limit = usageData.limits.team_member_limit;
          message = 'Team member limit reached. Please upgrade your plan to add more team members.';
          break;
        case 'aiCredits':
          currentUsage = usageData.usage.ai_credits_used + additionalUsage;
          limit = usageData.limits.ai_credits_limit;
          message = 'AI credits limit reached. Please upgrade your plan to get more AI credits.';
          break;
      }

      return {
        isLimitReached: currentUsage >= limit,
        message,
        currentUsage,
        limit,
        remainingUsage: Math.max(0, limit - currentUsage)
      };
    } catch (err) {
      console.error(`Unexpected error in checkLimitReached for ${limitType}:`, err);
      return null;
    }
  }
}
