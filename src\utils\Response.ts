import type { Context } from 'hono'

export const sendApiResponse = (c: Context, data: any, status: number = 200, headers: Record<string, string> = {}) => {
  headers = {
    'Content-Type': 'application/json',
    ...headers,
  }
  
  return c.json({ success: status < 400, data }, status as 200 | 201 | 400 | 500, headers)
}

export const sendApiError = (c: Context, error: string, status: number = 500, headers: Record<string, string> = {}) => {
  headers = {
    'Content-Type': 'application/json',
    ...headers,
  }
  
  return c.json({ success: false, error }, status as 400 | 500, headers)
}

