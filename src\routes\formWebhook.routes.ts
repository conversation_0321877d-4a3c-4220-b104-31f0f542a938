import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { 
  createFormWebhook, 
  getFormWebhooks, 
  updateFormWebhook, 
  deleteFormWebhook 
} from "../controllers/formWebhook.controllers";

const formWebhook = new Hono();

// Create a new webhook
formWebhook.post("/", verifySupabaseAuth, createFormWebhook);

// Get all webhooks for a form
formWebhook.get("/:formId", verifySupabaseAuth, getFormWebhooks);

// Update a webhook
formWebhook.put("/:id", verifySupabaseAuth, updateFormWebhook);

// Delete a webhook
formWebhook.delete("/:id", verifySupabaseAuth, deleteFormWebhook);

export { formWebhook };