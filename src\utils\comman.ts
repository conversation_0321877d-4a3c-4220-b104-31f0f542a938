const mergeFieldsWithAnswers = (fields: any[], answers: any[]) => {
    return fields?.map((field) => {
        const answer = answers?.find((a) => a.id === field.id);
        return {
            id: field.id,
            name: field.name,
            type: field.type,
            component: field.component,
            value: answer ? answer.value : null, // Ensure null if no value exists
        };
    });
};

const flattenValue = (value: any): string => {
    if (typeof value === "object" && value !== null) {
      if ("firstName" in value && "lastName" in value) {
        return `${value.firstName} ${value.lastName}`.trim(); // Ensure Last Name comes first
      }
  
      return Object.values(value).join(" "); // Default: Convert object values to a space-separated string
    }
    return value !== null && value !== undefined ? String(value) : "";
  };
export { mergeFieldsWithAnswers ,flattenValue};