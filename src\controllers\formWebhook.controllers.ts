import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

// Create a new webhook for a form
const createFormWebhook = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { form_id, webhook_url, integration_id } = body;

    if (!form_id || !webhook_url) {
      return sendApiError(c, "Form ID and webhook URL are required", 400);
    }

    // Verify the form belongs to the user's workspace
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("workspace_id")
      .eq("id", form_id)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    // Get user's workspace_id
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    // Verify user has access to this form
    if (form.workspace_id !== userProfile.workspace_id) {
      return sendApiError(c, "You don't have access to this form", 403);
    }

    // Create the webhook
    const { data: webhook, error: webhookError } = await supabase
      .from("automate_forms_webhook")
      .insert({
        form_id,
        webhook_url,
        workspace_id: form.workspace_id,
        integration_id: integration_id || null,
        status: true // Enable by default
      })
      .select()
      .single();

    if (webhookError) {
      console.error("Create Webhook Error:", webhookError);
      return sendApiError(c, "Failed to create webhook", 500);
    }

    return sendApiResponse(c, webhook, 201);
  } catch (err) {
    console.error("Create Webhook Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get all webhooks for a form
const getFormWebhooks = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("formId");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Get user's workspace_id
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    // Get webhooks for this form
    const { data: webhooks, error: webhooksError } = await supabase
      .from("automate_forms_webhook")
      .select("*")
      .eq("form_id", formId)
      .eq("workspace_id", userProfile.workspace_id);

    if (webhooksError) {
      console.error("Get Webhooks Error:", webhooksError);
      return sendApiError(c, "Failed to retrieve webhooks", 500);
    }

    return sendApiResponse(c, webhooks);
  } catch (err) {
    console.error("Get Webhooks Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Update a webhook
const updateFormWebhook = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const webhookId = c.req.param("id");
    if (!webhookId) {
      return sendApiError(c, "Webhook ID is required", 400);
    }

    const body = await c.req.json();
    const { webhook_url, status } = body;

    if (!webhook_url && status === undefined) {
      return sendApiError(c, "No fields to update", 400);
    }

    // Get user's workspace_id
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    // Verify webhook belongs to user's workspace
    const { data: webhook, error: webhookError } = await supabase
      .from("automate_forms_webhook")
      .select("*")
      .eq("id", webhookId)
      .eq("workspace_id", userProfile.workspace_id)
      .single();

    if (webhookError || !webhook) {
      return sendApiError(c, "Webhook not found or access denied", 404);
    }

    // Update the webhook
    const updateData: any = {};
    if (webhook_url) updateData.webhook_url = webhook_url;
    if (status !== undefined) updateData.status = status;

    const { data: updatedWebhook, error: updateError } = await supabase
      .from("automate_forms_webhook")
      .update(updateData)
      .eq("id", webhookId)
      .select()
      .single();

    if (updateError) {
      console.error("Update Webhook Error:", updateError);
      return sendApiError(c, "Failed to update webhook", 500);
    }

    return sendApiResponse(c, updatedWebhook);
  } catch (err) {
    console.error("Update Webhook Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Delete a webhook
const deleteFormWebhook = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const webhookId = c.req.param("id");
    if (!webhookId) {
      return sendApiError(c, "Webhook ID is required", 400);
    }

    // Get user's workspace_id
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "User profile not found", 404);
    }

    // Verify webhook belongs to user's workspace
    const { data: webhook, error: webhookError } = await supabase
      .from("automate_forms_webhook")
      .select("*")
      .eq("id", webhookId)
      .eq("workspace_id", userProfile.workspace_id)
      .single();

    if (webhookError || !webhook) {
      return sendApiError(c, "Webhook not found or access denied", 404);
    }

    // Delete the webhook
    const { error: deleteError } = await supabase
      .from("automate_forms_webhook")
      .delete()
      .eq("id", webhookId);

    if (deleteError) {
      console.error("Delete Webhook Error:", deleteError);
      return sendApiError(c, "Failed to delete webhook", 500);
    }

    return sendApiResponse(c, { message: "Webhook deleted successfully" });
  } catch (err) {
    console.error("Delete Webhook Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { createFormWebhook, getFormWebhooks, updateFormWebhook, deleteFormWebhook };