import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { 
  getWorkspaceRoles, 
  createRole, 
  updateRole, 
  deleteRole, 
} from "../controllers/role.controllers";

const role = new Hono();
role.use("*", verifySupabaseAuth);
// Role routes
role.get("/workspace/:workspace_id", getWorkspaceRoles);
role.post("/", createRole);
role.put("/:id", updateRole);
role.delete("/:id", deleteRole);



export { role };
