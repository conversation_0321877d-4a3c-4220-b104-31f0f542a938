import { Hono } from "hono";
import {updateFormSettings,updateThankYouPageSettings,getThankYouPageSettings} from "../controllers/formsetting.controllers"
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const formsetting = new Hono();
formsetting.put("/:id",verifySupabaseAuth,updateFormSettings);
formsetting.put("/thankyoupage/:id",verifySupabaseAuth,updateThankYouPageSettings);
formsetting.get("/thankyoupage/:id",verifySupabaseAuth,getThankYouPageSettings);
export {formsetting};
