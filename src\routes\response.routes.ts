import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { getResponse,deleteResponse,exportFormResponses,searchResponse ,deleteMultiSelectResponse} from "../controllers/response.controllers";
const response = new Hono();
response.get("/:form_id", verifySupabaseAuth,getResponse);
response.get("/export/:form_id", verifySupabaseAuth,exportFormResponses);
response.delete("/:response_id", verifySupabaseAuth,deleteResponse);
response.post("/delete", verifySupabaseAuth,deleteMultiSelectResponse);
response.post("/search", verifySupabaseAuth,searchResponse);
export {response};
