
import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { addconnection, updateconnection,getTemplate,linkForm } from "../controllers/whatsapp.controllers";
const whatsapp = new Hono();
whatsapp.post('/addconnection', verifySupabaseAuth, addconnection);
whatsapp.put('/updateconnection', verifySupabaseAuth, updateconnection);
whatsapp.post('/gettemplate', verifySupabaseAuth, getTemplate);
whatsapp.post('/linkform', verifySupabaseAuth, linkForm);
export {whatsapp};
