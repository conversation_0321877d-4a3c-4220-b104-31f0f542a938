import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";



const linkFormToTask = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) return sendApiError(c, "Unauthorized", 401);
  
      let body;
      try {
        body = await c.req.json();
      } catch {
        return sendApiError(c, "Invalid JSON body", 400);
      }
  
      const { form_id, integration_id, credential_id, action_id, assignedTo,assignedby, column_mapped_data,category_id } = body;
  
      if (!form_id || !integration_id || !credential_id || !action_id || !assignedby || !assignedTo || !category_id) {
        return sendApiError(c, "Missing required parameters", 400);
      }
  
      const userId = user.user.id;
  
      // Check if integration already exists
      const { data: existingIntegration, error: checkError } = await supabase
        .from("form_integrations")
        .select("id")
        .eq("form_id", form_id)
        .eq("integration_id", integration_id)
        .eq("credential_id", credential_id)
        .single();
  
      const metadata = {
        assignedby,
        category_id,
        assignedTo,
      };
  
      if (existingIntegration) {
        // Update existing integration
        const { error: updateError } = await supabase
          .from("form_integrations")
          .update({
            action_id,
            metadata,
            mapped_data: column_mapped_data || null,
            updated_at: new Date().toISOString()
          })
          .eq("id", existingIntegration.id);
  
        if (updateError) {
          console.error("DB Update Error:", updateError);
          return sendApiError(c, "Failed to update form integration", 500);
        }
  
        return sendApiResponse(c, { message: "Form integration updated successfully" }, 200);
      }
  
      // Insert new integration if it doesn't exist
      const { error } = await supabase
        .from("form_integrations")
        .insert({
          form_id,
          integration_id,
          credential_id,
          action_id,
          created_by: userId,
          metadata,
          mapped_data: column_mapped_data || null
        });
  
      if (error) {
        console.error("DB Insert Error:", error);
        return sendApiError(c, "Failed to save form integration", 500);
      }
  
      return sendApiResponse(c, { message: "Form integration linked successfully" }, 200);
    } catch (err) {
      console.error("Linkform Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };


export {
    linkFormToTask
};