import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { getWorkspaceUsageReport } from "../controllers/usage.controllers";

const usage = new Hono();

// Get workspace usage statistics
// usage.get("/:workspace_id", verifySupabaseAuth, getWorkspaceUsage);
// Get detailed workspace usage report with subscription info
usage.get("/report/:workspace_id", verifySupabaseAuth, getWorkspaceUsageReport);

export { usage };

