import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { hasAutomateFormAdminAccess } from "../utils/RoleUtils";

// Get all automateform members for a workspace
const getAutomateformMembers = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { workspace_id } = c.req.param();
    const workspaceId = workspace_id
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Get automateform members with their roles
    const { data: members, error: membersError } = await supabase
      .from("automateform_members")
      .select(`
        id,
        workspace_member_id,
        role_id,
        status,
        joined_at,
        user_id,
        workspace_id,
        automateform_role (
          id,
          name,
          description
        ),
        user_profile (
          id,
          first_name,
          last_name,
          email,
          phone,
          profile_image
        )
      `)
      .eq("workspace_id", workspaceId);

    if (membersError) {
      console.error("Get Automateform Members Error:", membersError);
      return sendApiError(c, "Failed to fetch automateform members", 500);
    }

    // Get form counts and recent activity for each member
    const membersWithStats = await Promise.all(members.map(async (member) => {
      // Get form count created by this user
      const { count: formCount, error: formCountError } = await supabase
        .from("automate_forms")
        .select("*", { count: "exact" })
        .eq("created_by", member.user_id);

      if (formCountError) {
        console.error("Form Count Error:", formCountError);
      }

      // Get submission count for forms created by this user
      const { data: formIds, error: formIdsError } = await supabase
        .from("automate_forms")
        .select("id")
        .eq("created_by", member.user_id);

      let submissionCount = 0;
      if (!formIdsError && formIds && formIds.length > 0) {
        const formIdArray = formIds.map(form => form.id);
        const { count: subCount, error: subCountError } = await supabase
          .from("automate_form_responses")
          .select("*", { count: "exact" })
          .in("form_id", formIdArray);

        if (!subCountError) {
          submissionCount = subCount || 0;
        }
      }

      // Get recent activity (last 5 forms created)
      const { data: recentForms, error: recentFormsError } = await supabase
        .from("automate_forms")
        .select("id, title, created_at, type")
        .eq("created_by", member.user_id)
        .order("created_at", { ascending: false })
        .limit(5);

      if (recentFormsError) {
        console.error("Recent Forms Error:", recentFormsError);
      }

      return {
        ...member,
        stats: {
          form_count: formCount || 0,
          submission_count: submissionCount,
          recent_activity: recentForms || []
        }
      };
    }));

    return sendApiResponse(c, { members: membersWithStats });
  } catch (err) {
    console.error("Unexpected Error in Fetching Automateform Members:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Add a workspace member to automateform members
const addWorkspaceMemberToAutomateform = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { workspace_member_id, role_id, workspace_id } = body;

    if (!workspace_member_id || !role_id || !workspace_id) {
      return sendApiError(c, "Workspace member ID, role ID, and workspace ID are required", 400);
    }

    // Check if the workspace member exists
    const { data: workspaceMember, error: memberError } = await supabase
      .from("workspace_members")
      .select("id, user_id")
      .eq("id", workspace_member_id)
      .eq("workspace_id", workspace_id)
      .single();

    if (memberError || !workspaceMember) {
      return sendApiError(c, "Workspace member not found", 404);
    }

    // Check if the role exists and belongs to the workspace
    const { data: role, error: roleError } = await supabase
      .from("automateform_role")
      .select("id")
      .eq("id", role_id)
      .eq("workspace_id", workspace_id)
      .single();

    if (roleError || !role) {
      return sendApiError(c, "Role not found in this workspace", 404);
    }


      const { data: newMember, error: insertError } = await supabase
        .from("automateform_members")
        .insert({
          workspace_member_id,
          role_id,
          status: "active",
          joined_at: new Date().toISOString(),
          user_id: workspaceMember.user_id,
          workspace_id
        })
        .select()
        .single();

      if (insertError) {
        console.error("Insert Automateform Member Error:", insertError);
        return sendApiError(c, "Failed to add member to automateform", 500);
      }
      
    // Update workspace member to set task_access to true
    const { error: updateWorkspaceMemberError } = await supabase
      .from("workspace_members")
      .update({ form_access: true })
      .eq("id", workspace_member_id);

    if (updateWorkspaceMemberError) {
      console.error("Update Workspace Member Error:", updateWorkspaceMemberError);
      // We don't want to fail the entire operation if just the task_access update fails
      // Just log the error and continue
    }    
    return sendApiResponse(c, {
      message: `Member added automateform successfully`,
      member: newMember
    },  201);
  } catch (err) {
    console.error("Add Member to Automateform Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const changeRole = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { automateform_member_id, role_id } = body;

    if (!automateform_member_id || !role_id) {
      return sendApiError(c, "Automateform member ID and role ID are required", 400);
    }

    // Check if the automateform member exists
    const { data: automateformMember, error: memberError } = await supabase
      .from("automateform_members")
      .select("id,workspace_id")
      .eq("id", automateform_member_id)
      .single();
    if (memberError || !automateformMember) {
      return sendApiError(c, "Automateform member not found", 404);
    }

    // Check if the role exists
    const { data: role, error: roleError } = await supabase
      .from("automateform_role")
      .select("id,workspace_id")
      .eq("id", role_id)
      .single();

    if (roleError || !role) {
      return sendApiError(c, "Role not found", 404);
    }

    if (role.workspace_id !== automateformMember.workspace_id) {
      return sendApiError(c, "Role does not belong to the same workspace as the automateform member", 400);
    }

    // Check if user is an admin
    const isAdmin = await hasAutomateFormAdminAccess(
      user.user.id,
      automateformMember.workspace_id
    );
    console.log("isAdmin",isAdmin)
    if (!isAdmin) {
      return sendApiError(c, "Only admin can change roles", 403);
    }

    // Update the role
    const { error: updateError } = await supabase
      .from("automateform_members")
      .update({ role_id })
      .eq("id", automateform_member_id);

    if (updateError) {  
      console.error("Update Role Error:", updateError);
      return sendApiError(c, "Failed to update role", 500);
    }

    return sendApiResponse(c, {
      message: "Role updated successfully",
    });
  } catch (err) {
    console.error("Update Role Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const removeMemberFromAutomateform = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const automateform_member_id = c.req.param("id");
    if (!automateform_member_id) {
      return sendApiError(c, "Automateform member ID is required", 400);
    }

    // Check if the automateform member exists    
    const { data: automateformMember, error: memberError } = await supabase
      .from("automateform_members")
      .select("id, workspace_id, workspace_member_id, user_id")
      .eq("id", automateform_member_id)
      .single();

    if (memberError || !automateformMember) {
      return sendApiError(c, "Automateform member not found", 404);
    }

    // Check if user is an admin
    const isAdmin = await hasAutomateFormAdminAccess(
      user.user.id,
      automateformMember.workspace_id
    );
    console.log("isAdmin",isAdmin)
    if (!isAdmin) {
      return sendApiError(c, "Only administrators can remove members", 403);
    }

    // Delete the automateform member
    const { error: deleteError } = await supabase
      .from("automateform_members")
      .delete() 
      .eq("id", automateform_member_id);

    if (deleteError) {
      console.error("Delete Automateform Member Error:", deleteError);
      return sendApiError(c, "Failed to remove member from automateform", 500);
    }

    // Update workspace member to set form_access to false
    const { error: updateWorkspaceMemberError } = await supabase
      .from("workspace_members")
      .update({ form_access: false })
      .eq("id", automateformMember.workspace_member_id);

    if (updateWorkspaceMemberError) {
      console.error("Update Workspace Member Error:", updateWorkspaceMemberError);
      return sendApiError(c, "Failed to update workspace member", 500);
    }

    return sendApiResponse(c, {
      message: "Member removed from automateform successfully",
    });
  } catch (err) {
    console.error("Remove Member from Automateform Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};





export {
  getAutomateformMembers,
  addWorkspaceMemberToAutomateform,
  changeRole,
  removeMemberFromAutomateform
};

