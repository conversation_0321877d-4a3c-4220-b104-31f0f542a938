import type { Context } from "hono";
import { supabase } from "../db/index.ts";
import { sendApiError } from "../utils/Response";
import { getCookie } from "hono/cookie";
import { HTTPException } from "hono/http-exception";
export const verifySupabaseAuth = async (
  c: Context,
  next: () => Promise<void>
) => {
  try {
    const token =
      getCookie(c, "access_token") ||
      c.req.header("Authorization")?.replace("Bearer ", "");

    if (!token) {
     return sendApiError(c, "Unauthorized",401);
    }

    const { data: user, error } = await supabase.auth.getUser(token);
    if (error || !user) {
      return sendApiError(c, "Unauthorized Invalid Token",401);
    }
  //  console.log("🔐 User authenticated:", user);
    c.set("user", user);
    await next();
  } catch (err) {
    console.error("Error in verifySupabaseAuth:", err);
    return sendApiError(c, "Internal server error",500);
  }
};
