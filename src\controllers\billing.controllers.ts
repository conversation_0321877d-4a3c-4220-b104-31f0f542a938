import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

// Get workspace wallet balance
 const getWalletBalance = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const workspaceId = c.req.param("workspace_id");
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Check if user belongs to the workspace
    const { data: memberData, error: memberError } = await supabase
      .from("workspace_members")
      .select("id")
      .eq("workspace_id", workspaceId)
      .eq("user_id", user.user.id)
      .single();

    if (memberError || !memberData) {
      return sendApiError(c, "You don't have access to this workspace", 403);
    }

    // Get wallet balance
    const { data: wallet, error: walletError } = await supabase
      .from("wallets")
      .select("id, balance, currency, updated_at")
      .eq("workspace_id", workspaceId)
      .single();

    if (walletError) {
      // If wallet doesn't exist, create one
      if (walletError.code === "PGRST116") {
        const { data: newWallet, error: createError } = await supabase
          .from("workspace_wallet")
          .insert([{ workspace_id: workspaceId }])
          .select("id, balance, currency, updated_at")
          .single();

        if (createError) {
          console.error("Create Wallet Error:", createError);
          return sendApiError(c, "Failed to create workspace wallet", 500);
        }

        return sendApiResponse(c, newWallet);
      }

      console.error("Get Wallet Error:", walletError);
      return sendApiError(c, "Failed to retrieve wallet information", 500);
    }

    return sendApiResponse(c, wallet);
  } catch (err) {
    console.error("Get Wallet Balance Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Add funds to workspace wallet
 const addFundsToWallet = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const workspaceId = c.req.param("workspace_id");
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Check if user has admin rights in the workspace
    const { data: memberData, error: memberError } = await supabase
      .from("workspace_members")
      .select("id, role")
      .eq("workspace_id", workspaceId)
      .eq("user_id", user.user.id)
      .single();

    if (memberError || !memberData) {
      return sendApiError(c, "You don't have access to this workspace", 403);
    }

    if (memberData.role !== "admin") {
      return sendApiError(c, "Only admins can add funds to the wallet", 403);
    }

    const body = await c.req.json();
    if (!body.amount || body.amount <= 0) {
      return sendApiError(c, "Valid amount is required", 400);
    }

    const amount = parseFloat(body.amount);
    const currency = body.currency || "USD";
    const paymentMethod = body.payment_method || null;
    const type = body.type || "recharge";
    const referenceNumber = body.reference_number || null;
    const remarks = body.description || "Wallet top-up";

    // Add transaction record - this will trigger the wallet update automatically
    const { data: transaction, error: transactionError } = await supabase
      .from("automate_form_transactions")
      .insert([
        {
          workspace_id: workspaceId,
          amount: amount,
          currency: currency,
          type: type,
          status: "success",
          payment_method: paymentMethod,
          reference_number: referenceNumber,
          remarks: remarks,
        },
      ])
      .select("id")
      .single();

    if (transactionError) {
      console.error("Transaction Error:", transactionError);
      return sendApiError(c, "Failed to record transaction", 500);
    }

    return sendApiResponse(c, {
      message: "Funds added successfully",
      transaction_id: transaction.id,
    });
  } catch (err) {
    console.error("Add Funds Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get workspace usage
 const getWalletTransactions = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const workspaceId = c.req.param("workspace_id");
     const limit = parseInt(c.req.query("limit") || "10", 10); // Default limit to 10
    const offset = parseInt(c.req.query("offset") || "0", 10);
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Check if user belongs to the workspace
    const { data: memberData, error: memberError } = await supabase
      .from("workspace_members")
      .select("id")
      .eq("workspace_id", workspaceId)
      .eq("user_id", user.user.id)
      .single()
      

    if (memberError || !memberData) {
      return sendApiError(c, "You don't have access to this workspace", 403);
    }

    // Get current usage and limits
    const { data: transactions, count, error: usageError } = await supabase
      .from("automate_form_transactions")
      .select("amount,type,reference_number,remarks,created_at,status",{ count: "exact" })
      .eq("workspace_id", workspaceId)
      .range(offset, offset + limit - 1);

    if (usageError) {
      console.error("Get Usage Error:", usageError);
      return sendApiError(c, "Failed to retrieve usage information", 500);
    }

    return sendApiResponse(c, {
      transactions,
      total_count: count || 0
    });
  } catch (err) {
    console.error("Get Usage Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const createOrder = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const workspaceId = c.req.param("workspace_id");
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Check if user belongs to the workspace
    const { data: memberData, error: memberError } = await supabase
      .from("workspace_members")
      .select("id")
      .eq("workspace_id", workspaceId)
      .eq("user_id", user.user.id)
      .single();

    if (memberError || !memberData) {
      return sendApiError(c, "You don't have access to this workspace", 403);
    }

    const body = await c.req.json();
    const num_users=1
    // Validate required fields
    const requiredFields = ['first_name', 'last_name', 'email', 'phone', 'country', 'country_code', 'plan_name', 'amount'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return sendApiError(c, `${field} is required`, 400);
      }
    }

    // Create order record
    const orderData = {
      first_name: body.first_name,
      last_name: body.last_name,
      email: body.email,
      phone: body.phone,
      country: body.country,
      country_code: body.country_code,
      plan_name: body.plan_name,
      num_users:num_users,
      amount: parseFloat(body.amount),
      coupon_code: body.coupon_code || null,
      status: null,
      workspace_id: workspaceId,
      user: null,
      frequency: body.frequency || 'monthly',
      currency: body.currency || 'INR',
      remkarks: body.remarks || null
    };

    // Insert order - this will trigger the before_insert_billing_orders trigger
    // which will create the Razorpay order and populate rzp_order_id
    const { data: order, error: orderError } = await supabase
      .from("billing_orders")
      .insert([orderData])
      .select("id, order_id, rzp_order_id, status, amount, currency")
      .single();

    if (orderError) {
      console.error("Order Creation Error:", orderError);
      return sendApiError(c, "Failed to create order", 500);
    }

    return sendApiResponse(c, {
      order_id: order.order_id,
      rzp_order_id: order.rzp_order_id,
      status: order.status,
      amount: order.amount,
      currency: order.currency
    });
  } catch (err) {
    console.error("Create Order Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get plans by module ID
const getModulePlans = async (c: Context) => {
  try {
    const moduleId = c.req.param("module_id");
    if (!moduleId) {
      return sendApiError(c, "Module ID is required", 400);
    }

    // Fetch module plans
    const { data: plans, error } = await supabase
      .from("app_modules")
      .select("id, name, yearly_price, three_year_price,five_year_price, caption, status, features, usd_price, code, workspace_limit,monthly_price")
      .eq("app", 5)
      .order("sequence");

    if (error) {
      console.error("Get Module Plans Error:", error);
      return sendApiError(c, "Failed to retrieve module plans", 500);
    }

    return sendApiResponse(c, { plans });
  } catch (err) {
    console.error("Get Module Plans Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { getWalletBalance, addFundsToWallet, getWalletTransactions, createOrder, getModulePlans };

