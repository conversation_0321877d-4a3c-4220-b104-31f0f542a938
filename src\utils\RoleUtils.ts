import { supabase } from "../db";

/**
 * Check if a user has admin access in the AutomateForm application
 * 
 * @param userId The user ID to check
 * @param workspaceId The workspace ID to check
 * @returns Promise<boolean> True if the user has admin access, false otherwise
 */
export const hasAutomateFormAdminAccess = async (userId: string, workspaceId: string | number): Promise<boolean> => {
  try {
    // First check if the user has a role in automateform_members with admin privileges
    const { data: automateFormMember, error: memberError } = await supabase
      .from("automateform_members")
      .select(`
        role_id,
        automateform_role (
          name
        ),
        workspace_member_id,
        status
      `)
      .eq("status", "active")
      .eq("user_id", userId)
      .eq("workspace_id", workspaceId)
      .single();
    console.log("automateFormMember",automateFormMember)
    // If the user has an automateform role and it's an admin role, they have admin access
    if (!memberError && automateFormMember && 
        automateFormMember.automateform_role && 
        automateFormMember.automateform_role.name === "admin") {
      return true;
    }

    return false;
  } catch (err) {
    console.error("Error checking admin access:", err);
    return false;
  }
};

/**
 * Check if a user has a specific permission in the AutomateForm application
 * 
 * @param userId The user ID to check
 * @param workspaceId The workspace ID to check
 * @param permissionCode The permission code to check
 * @returns Promise<boolean> True if the user has the permission, false otherwise
 */
export const hasAutomateFormPermission = async (
  userId: string, 
  workspaceId: string | number,
  permissionCode: string
): Promise<boolean> => {
  try {
    // First check if the user has admin access (admins have all permissions)
    const isAdmin = await hasAutomateFormAdminAccess(userId, workspaceId);
    if (isAdmin) {
      return true;
    }

    // Get the user's workspace member ID
    const { data: workspaceMember, error: workspaceError } = await supabase
      .from("workspace_members")
      .select("id")
      .eq("user_id", userId)
      .eq("workspace_id", workspaceId)
      .single();

    if (workspaceError || !workspaceMember) {
      return false;
    }

    // Check if the user has a role in automateform_members
    const { data: automateFormMember, error: memberError } = await supabase
      .from("automateform_members")
      .select(`
        role_id
      `)
      .eq("status", "active")
      .eq("workspace_member_id", workspaceMember.id)
      .single();

    if (memberError || !automateFormMember || !automateFormMember.role_id) {
      return false;
    }

    // Check if the user's role has the required permission
    const { data: rolePermission, error: permissionError } = await supabase
      .from("automateform_role_permission")
      .select(`
        automateform_permission (
          code
        )
      `)
      .eq("role_id", automateFormMember.role_id);

    if (permissionError || !rolePermission) {
      return false;
    }

    // Check if any of the role's permissions match the required permission code
    const hasPermission = rolePermission.some(
      rp => rp.automateform_permission && rp.automateform_permission.code === permissionCode
    );

    return hasPermission;
  } catch (err) {
    console.error("Error checking permission:", err);
    return false;
  }
};
