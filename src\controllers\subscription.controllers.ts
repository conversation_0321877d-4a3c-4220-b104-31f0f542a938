import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

// Purchase subscription for a module
const purchaseSubscription = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();
    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const body = await c.req.json();
    const {
      module_id,
      num_users,
      plan_duration,
      currency = "INR",
    } = body;

    if ( !module_id || !num_users || !plan_duration) {
      return sendApiError(c, "Missing required fields", 400);
    }

    // Get module details
    const { data: module, error: moduleError } = await supabase
      .from("app_modules")
      .select("*")
      .eq("id", module_id)
      .single();

    console.log(module);
    if (moduleError || !module) {
      console.error("Get Module Error:", moduleError);
      return sendApiError(c, "Failed to retrieve module information", 500);
    }

    // Calculate price based on plan duration
    let price = 0;
    let validityMonths = 0;

    if (plan_duration === "monthly") {
      price = module.monthly_price * num_users;
      validityMonths = 1;
    } else if (plan_duration === "yearly") {
      price = module.yearly_price * num_users;
      validityMonths = 12;
    } else if (plan_duration === "three_year") {
      price = module.three_year_price * num_users;
      validityMonths = 36;
    } else if (plan_duration === "five_year") {
      price = module.five_year_price * num_users;
      validityMonths = 60;
    } else {
      return sendApiError(c, "Invalid plan duration", 400);
    }

    // Check wallet balance
    const { data: wallet, error: walletError } = await supabase
      .from("wallets")
      .select("balance")
      .eq("workspace_id", userdata.workspace_id)
      .single();

    if (walletError || !wallet) {
      console.error("Get Wallet Error:", walletError);
      return sendApiError(c, "Failed to retrieve wallet information", 500);
    }

    // Check if balance is sufficient
    if (wallet.balance < price) {
      return sendApiError(
        c,
        "Insufficient wallet balance. Please recharge your wallet.",
        400
      );
    }

    // Create transaction
    const {  error: transactionError } = await supabase
      .from("automate_form_transactions")
      .insert([
        {
          workspace_id: userdata.workspace_id,
          amount: price, // Negative amount for deduction
          currency: currency || "INR",
          type: "subscription",
          status: "success",
          payment_method: "wallet",
          remarks: `Subscription for ${module.name} - ${plan_duration} plan for ${num_users} users`,
        },
      ])
      .select("id")
      .single();

    if (transactionError) {
      console.error("Create Transaction Error:", transactionError);
      return sendApiError(c, "Failed to create transaction", 500);
    }

    // Calculate validity date
    const validityDate = new Date();
    validityDate.setMonth(validityDate.getMonth() + validityMonths);
    console.log(validityDate);
    // Create or update subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("automate_form_module_subscription")
      .upsert(
        [
          {
            module: module_id,
            workspace_id: userdata.workspace_id,
            num_users,
            validity: validityDate.toISOString(),
            status: "active",
            remarks: `${plan_duration} plan`,
            updated_at: new Date().toISOString(),
            updated_by: user.user.id,
            app: module.app
          },
        ],
        {
          onConflict: "workspace_id,app",
        }
      )
      .select()
      .single();

    if (subscriptionError) {
      console.error("Create Subscription Error:", subscriptionError);
      return sendApiError(c, "Failed to create subscription", 500);
    }


    return sendApiResponse(c, {
      message: "Subscription purchased successfully",
      subscription,
    });
  } catch (err) {
    console.error("Purchase Subscription Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get active subscriptions for a workspace
const getWorkspaceSubscriptions = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();
    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const { data: subscriptions, error } = await supabase
      .from("automate_form_module_subscription")
      .select(
        `
        id, 
        num_users, 
        validity, 
        status, 
        remarks,
        module(id, name, code, caption, features)
      `
      )
      .eq("workspace_id", userdata.workspace_id)
      .eq("status", "active");

    if (error) {
      console.error("Get Subscriptions Error:", error);
      return sendApiError(c, "Failed to retrieve subscriptions", 500);
    }

    return sendApiResponse(c, { subscriptions });
  } catch (err) {
    console.error("Get Subscriptions Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Manually renew a subscription
const renewSubscription = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const subscriptionId = c.req.param("subscription_id");

    // Get subscription details
    const { data: subscription, error: subError } = await supabase
      .from("automate_form_module_subscription")
      .select(
        `
        id, 
        module,
        workspace_id,
        num_users,
        validity,
        status,
        remarks
      `
      )
      .eq("id", subscriptionId)
      .single();

    if (subError || !subscription) {
      console.error("Get Subscription Error:", subError);
      return sendApiError(c, "Failed to retrieve subscription", 500);
    }

    // Get module details
    const { data: module, error: moduleError } = await supabase
      .from("app_modules")
      .select("*")
      .eq("id", subscription.module)
      .single();

    if (moduleError || !module) {
      console.error("Get Module Error:", moduleError);
      return sendApiError(c, "Failed to retrieve module information", 500);
    }

    // Determine if it's a monthly plan
    const isMonthly = subscription.remarks.includes("monthly");

    // Calculate renewal price
    const price = isMonthly
      ? module.monthly_price * subscription.num_users
      : (module.yearly_price * subscription.num_users) / 12; // Prorated monthly cost

    // Check wallet balance
    const { data: wallet, error: walletError } = await supabase
      .from("wallets")
      .select("balance")
      .eq("workspace_id", subscription.workspace_id)
      .single();

    if (walletError || !wallet) {
      console.error("Get Wallet Error:", walletError);
      return sendApiError(c, "Failed to retrieve wallet information", 500);
    }

    // Check if balance is sufficient
    if (wallet.balance < price) {
      return sendApiError(
        c,
        "Insufficient wallet balance. Please recharge your wallet.",
        400
      );
    }

    // Create transaction
    const { data: transaction, error: transactionError } = await supabase
      .from("automate_form_transactions")
      .insert([
        {
          workspace_id: subscription.workspace_id,
          amount: -price,
          currency: "INR",
          status: "success",
          type: "subscription_renewal",
          payment_method: "wallet",
          remarks: `Manual renewal for ${module.name} - ${isMonthly ? "monthly" : "yearly"} plan`,
        },
      ])
      .select("id")
      .single();

    if (transactionError) {
      console.error("Create Transaction Error:", transactionError);
      return sendApiError(c, "Failed to create transaction", 500);
    }

    // Calculate new validity date
    const validityDate = new Date(subscription.validity);
    validityDate.setMonth(validityDate.getMonth() + (isMonthly ? 1 : 12));

    // Update subscription
    const { data: updatedSub, error: updateError } = await supabase
      .from("automate_form_module_subscription")
      .update({
        validity: validityDate.toISOString(),
        status: "active",
        updated_at: new Date().toISOString(),
        updated_by: user.user.id,
      })
      .eq("id", subscriptionId)
      .select()
      .single();

    if (updateError) {
      console.error("Update Subscription Error:", updateError);
      return sendApiError(c, "Failed to update subscription", 500);
    }

    return sendApiResponse(c, {
      message: "Subscription renewed successfully",
      subscription: updatedSub,
    });
  } catch (err) {
    console.error("Renew Subscription Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { purchaseSubscription, getWorkspaceSubscriptions, renewSubscription };
