import type { Context } from "hono";

const asyncHandler =(fn: (c: Context, next: () => Promise<void>) => Promise<void>) =>
  async (c: Context, next: () => Promise<void>) => {
    try {
      await fn(c, next);
    } catch (error: any) {
      return c.json(
        {
          success: false,
          message: error.message || "Internal Server Error",
        },
        error.code || 500
      );
    }
  };

export { asyncHandler };
